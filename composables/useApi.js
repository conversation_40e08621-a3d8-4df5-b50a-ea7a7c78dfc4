import {hash} from 'ohash';

export async function useApi(url, fetchOptions = {}, options = {}) {
	// cache key. Important: must be declared before token to avoid hash mismatch
	let key = options?.key ? url + hash(['api-fetch', url, options.key]) : url + hash(['api-fetch', url, fetchOptions]);
	if (options?.dataKey) key = options.dataKey;

	// get cached data
	const cachedData = useNuxtData(key);
	if (options?.cache && cachedData?.data?.value) {
		return options?.fetchMode == 'reactive' ? cachedData : cachedData.data.value;
	}

	const nuxtApp = useNuxtApp();
	const t = useToken();
	const lang = useLang();
	const config = useAppConfig();
	let baseURL = url.startsWith('/api/nuxtapi/') ? '' : config.host;
	let authToken = fetchOptions?.token ? fetchOptions.token : t.get();

	const fetchConfig = {
		headers: {
			'Authorization': 'Bearer ' + authToken,
		},
		baseURL: baseURL,
		key: key,
		...fetchOptions,
	};

	// add lang to body if POST method
	if (fetchOptions?.method == 'POST' || fetchOptions?.method == 'PUT') {
		if (!fetchOptions?.body) fetchConfig.body = {};
		let appendLang = true;
		if (fetchOptions.method == 'POST' && (!config.fetchApi?.post?.appendLang || options?.appendLang === false)) appendLang = false;
		if (fetchOptions.method == 'PUT' && (!config.fetchApi?.put?.appendLang || options?.appendLang === false)) appendLang = false;
		if (fetchConfig.body && options?.cache == false) fetchConfig.body.ignoreCache = true;
		if (appendLang) fetchConfig.body.lang = lang.get();
	}

	// add lang to url if GET method and appendLang
	if ((!fetchOptions?.method || fetchOptions?.method == 'GET') && options?.appendLang) {
		if (!url.includes('?')) {
			url += '?lang=' + lang.get();
		} else if (!url.includes('lang=')) {
			url += '&lang=' + lang.get();
		}
	}

	const handleError = async status => {
		if (!status) return;
		if (status === 401 || status === 503) {
			if (process.client) {
				t.removeToken();
				return window.location.reload();
			}
			throw createError({
				statusCode: status,
				statusMessage: `Error ${status}`,
			});
		}
	};

	// use default Nuxt useFetch
	if (options?.fetchMode == 'reactive') {
		const res = await useFetch(url, fetchConfig);
		await handleError(res.error?.value?.statusCode);
		return res;
	}

	// use vanilla ofetch by default
	try {
		const res = await $fetch(url, fetchConfig);
		let data = res;
		if (fetchOptions?.transform) data = fetchOptions.transform(res);
		if (options?.cache) nuxtApp.payload.data[key] = data;
		return data;
	} catch (error) {
		await handleError(error.status);
		return {
			...error.data,
			statusCode: error.status,
		};
	}
}
