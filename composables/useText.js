export function useText() {
	const config = useAppConfig();
	const lang = useLang();

	// strip html tags from string
	function stripHtml(value) {
		if (!value) return '';

		// Replace block-level elements with a space before removing all tags
		const withSpaces = value.replace(/<\/?(p|div|br|h[1-6]|li|ul|ol|table|tr|td|th|blockquote)>/gi, ' ');

		// Remove remaining HTML tags
		const withoutTags = withSpaces.replace(/(<([^>]+)>)/gi, '');

		// Replace multiple spaces or newlines with a single space
		return withoutTags.replace(/\s+/g, ' ').trim();
	}

	// limit number of characters
	function limitCharacter(value, limit = 50, append = '...') {
		if (value) {
			let trimmedString = value.trim().substr(0, limit);
			const dots = value.length > limit ? append : '';

			return trimmedString + dots;
		}
	}

	// limit number of words
	function limitWords(value, limit = 20, append = '...') {
		if (value) {
			let words = value.trim().split(' ');
			const num = words && words.length > limit ? append : '';

			words = words.slice(0, limit).join(' ') + num;
			return words;
		}
	}

	// format date
	function formatDate(value, format = 'DD.MM.YYYY.', options) {
		let d;

		// check if value is timestamp or date string
		const timestampRegex = /^\d+$/;
		if (timestampRegex.test(value)) {
			d = new Date(parseInt(value) * 1000);
		} else if (typeof value === 'string') {
			d = new Date(value);
		} else {
			useLog('formatDate: invalid date format', 'error');
			return null;
		}

		const locales = options?.locales || lang.get('locale');
		const dayName = new Intl.DateTimeFormat(locales, {weekday: 'long'}).format(d);
		const monthName = new Intl.DateTimeFormat(locales, {month: 'long'}).format(d);
		const shortMonthName = new Intl.DateTimeFormat(locales, {month: 'short'}).format(d).replace('.', '');
		const formatMap = {
			'dddd': dayName,
			'DD': String(d.getDate()).padStart(2, '0'),
			'MMMM': monthName,
			'MMM': shortMonthName,
			'MM': String(d.getMonth() + 1).padStart(2, '0'),
			'YYYY': d.getFullYear(),
			'HH': String(d.getHours()).padStart(2, '0'),
			'mm': String(d.getMinutes()).padStart(2, '0'),
			'ss': String(d.getSeconds()).padStart(2, '0'),
		};

		const formattedDate = format.replace(/dddd|DD|MMMM|MMM|MM|YYYY|HH|mm|ss/g, match => formatMap[match]);
		return formattedDate ? formattedDate : null;
	}

	// format number
	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat
	function formatNumber(value, options = {}) {
		const locale = options?.locale ? options.locale : 'hr-HR';
		return new Intl.NumberFormat(locale, {
			style: 'decimal',
			maximumSignificantDigits: 10,
			...options,
		}).format(value);
	}

	// process and replace relative image paths with absolute
	function absoluteImagePath(value, mode = 'content') {
		if (!value) return '';

		// process images uploaded to content editor. It will search for images that start with /upload/ and replace it with absolute path
		if (process.env.NODE_ENV === 'development' && mode == 'content') {
			const regex = /(?<=src=["']?|^)(\/upload\/[^"'\s]+?\.(png|jpg|jpeg|gif|svg|webp))/gim;
			return value.replace(regex, match => config.host + match);
		}

		// process provided string and replace relative paths with absolute
		if (mode == 'string') {
			value = value.toString();

			if (value.startsWith('catalog/') || value.startsWith('publish/') || value.startsWith('menuitem/') || value.startsWith('rotator_element/')) {
				value = '/upload/' + value;
			}

			if (process.env.NODE_ENV === 'development') {
				if (value.startsWith('http://localhost:3000')) {
					value = value.replace('http://localhost:3000', '');
				}

				if (value.startsWith('/upload/')) {
					value = value.replace('/upload/', config.host + '/upload/');
				}
			}
		}

		return value;
	}

	// wrap image if there is image description
	function wrapImages(options) {
		var defaults = {
			selector: 'image-border', // image class
			titleSelector: 'alt', // image description element
			wrapper: 'image-wrapper', // image wrapper div with provided class
			titleContainer: 'image-title', // image title div with provided class
			linkClass: 'image-link', // add class to image link element
		};

		// extend - allow user overwrite
		options = Object.assign({}, defaults, options);

		// query elements that should apply the function
		var elements = document.querySelectorAll('.' + options.selector);

		// if there are no elements, stop the function
		if (!elements) return;

		// loop through the elements
		elements.forEach(function (element) {
			var imageTitle = element.getAttribute(options.titleSelector);

			if (imageTitle) {
				// wrapping the image with a span
				var wrapper = document.createElement('span');
				wrapper.className = options.wrapper;
				element.parentNode.insertBefore(wrapper, element);
				wrapper.appendChild(element);

				// adding the title if it exists
				var titleContainer = document.createElement('span');
				titleContainer.className = options.titleContainer;
				titleContainer.textContent = imageTitle;
				wrapper.appendChild(titleContainer);
			}

			// if the parent's parent is a link, add a class
			if (element.parentNode.parentNode.tagName === 'A') {
				element.parentNode.parentNode.classList.add(options.linkClass);
			}
		});
	}

	// Highlight text within content based on search term
	function highlightText(content, searchTerm, options = {}) {
		if (!searchTerm) return content;
		const regex = new RegExp(searchTerm, 'gi');
		const highlightClass = options?.highlightClass ? options.highlightClass : 'text-highlight';
		return content.replace(regex, match => `<span class="${highlightClass}">${match}</span>`);
	}

	/*
	Convert special letters to normal letters.
	E.g. "fruhle" will also match "frühle", "skola" will also match "škola", etc
	*/
	function normalizeString(str) {
		return str
			.normalize('NFD')
			.replace(/[\u0300-\u036f]/g, '')
			.replace(/ä/g, 'a')
			.replace(/ö/g, 'o')
			.replace(/ü/g, 'u')
			.replace(/Ä/g, 'A')
			.replace(/Ö/g, 'O')
			.replace(/Ü/g, 'U')
			.replace(/ß/g, 'ss');
	}

	return {stripHtml, limitCharacter, limitWords, formatDate, formatNumber, absoluteImagePath, wrapImages, highlightText, normalizeString};
}
