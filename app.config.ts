import {config} from './hapi.config';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineAppConfig({
	host: config[config.default].host,
	lang: config[config.default].lang,
	baseCompatibility: '1.29.0',
	cache: {
		debug: false,
		enabled: config[config.default].apiCache,
		refresh: {
			currency: 1440, // 24 hours
			endpoints: 1440,
			info: 1440,
			labels: 10,
			menus: 30,
			redirects: 60,
			rotators: 10,
			routes: 5,
			token: 1440,
			newsletter: 1440,
			gdprTemplate: 1440,
			thumbs: 180,
			cmsPages: 60,
			catalogCategories: 5,
			catalogLists: 5,
			catalogManufacturers: 30,
			catalogProducts: 5,
			catalogProduct: 5,
			catalogSellers: 5,
			faqCategories: 60,
			faqQuestions: 60,
			faqQuestion: 60,
			publishCategories: 60,
			publishPost: 30,
			publishPosts: 30,
			locationPoints: 60,
			staticContent: 60,
		},
	},
	catalog: {
		productsResponseFields: [
			'active_shipping_options_count',
			'short_description',
			'attributes_special',
			'availability_info',
			'available_qty',
			'badges_special_1',
			'badges_special_2',
			'basic_price_custom',
			'category_title',
			'category_url_without_domain',
			'code',
			'date_available',
			'discount_percent_custom',
			'extra_price_cart_dynamicprice',
			'extra_price_dynamicprice',
			'extra_price_mode_dynamicprice',
			'feedback_comment_widget',
			'feedback_rate_widget',
			'id',
			'installments_list_data',
			'installments_calculation',
			'is_available',
			'is_rate',
			'last_piece_sale',
			'loyalty_price_custom',
			'main_image_description',
			'images',
			'main_image_title',
			'main_image_upload_path',
			'main_image_thumbs',
			'other_images',
			'price_custom',
			'product_condition',
			'quantity',
			'selected_price',
			'seller_id',
			'seller_title',
			'seller_url_product_page_without_domain',
			'seller_url_without_domain',
			'shipping_date',
			'shipping_options',
			'shopping_cart_code',
			'status',
			'title',
			'type',
			'url_without_domain',
			'warehouses_single_pickup_display',
			'xxx',
		],
		fetch: {
			check_lists: config.default === 'prod' || config.default === 'beta' ? 'webcatalog_157271,webcatalog_157270,webcatalog_201949,webcatalog_1219026' : 'webcatalog_157271,webcatalog_157270,webcatalog_201949,webcatalog_2225499',
			appconfig: 'product_pim_min_chars',
		},
		hooks: {
			mapProduct: product => {
				if (product?.type == 'configurable' && product?.initial_offer) {
					return product.initial_offer;
				}
				return product;
			},
		},
	},
	compare: {
		limit: 4,
	},	
	keycloak: true,
	auth: {
		loggedOutRedirect: 'home',
	},
	structuredData: {
		enabled: true,
		basic: {
			name: 'Big Bang',
			organizationName: 'Big Bang',
			logo: 'https://www.bigbang.si/media/images/logo-mail.png', // FIXME INTEG privremeni logo
			phone: '*********',
			email: '<EMAIL>',
			socialNetworkUrls: '"https://www.facebook.com/BigBangSlovenija?ref=hl","https://www.instagram.com/bigbangslo/","https://www.youtube.com/user/BigBangSlovenija","https://www.linkedin.com/company/big-bang-slovenija"',
		},
	},
	thumbPresets: {
		seller: {
			imageFields: ['main_image_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 200,
						height: 200,
						crop: false,
					},
				}
			],
		},
		catalogEntry: {
			imageFields: ['main_image_upload_path', 'other_images.file_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 400,
						height: 400,
						crop: false,
					},
					related: ['main_image_upload_path']
				},
				{
					alias: 'thumb_small',
					config: {
						width: 110,
						height: 110,
						crop: false,
					},
					related: ['other_images.file_upload_path']
				},
			],
		},
		catalogList: {
			imageFields: ['main_image_3'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 269,
						height: 477,
						crop: true,
					},
				},
			],
		},
		categories: {
			imageFields: ['image_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 200,
						height: 200,
						crop: false,
					},
				}
			],
		},
		catalogDetail: {
			imageFields: ['url', 'all_images.file'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 546,
						height: 546,
						crop: false,
					},
				},
				{
					alias: 'thumb_small',
					config: {
						width: 98,
						height: 98,
						crop: false,
					},
				},
				{
					alias: 'thumb_zoom',
					config: {
						width: 1000,
						height: 1000,
						crop: false,
					},
				},
			],
		},
		catalogEnergy: {
			imageFields: ['url', 'all_images.file', 'energy_image_upload_path'],
			thumbs: [
				{
					alias: 'thumb_energy',
					config: {
						width: 60,
						height: 60,
						crop: false,
					},
				},
				{
					alias: 'thumb_energy_gallery',
					config: {
						width: 800,
						height: 800,
						crop: false,
					},
				},
			],
		},
		promotion: {
			imageFields: ['main_image_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 800,
						height: 600,
						crop: true,
					},
				},	
			],
		},
		cart: {
			imageFields: ['image'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 400,
						height: 400,
						crop: false,
					},
				},
			],
		},
	}
});
