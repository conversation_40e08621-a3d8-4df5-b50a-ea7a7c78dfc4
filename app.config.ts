export default defineAppConfig({
	//host: 'https://...',
	version: '1.29.0.dev',
	lang: 'hr',
	multilanguage: false,
	api: 'hapi',
	apiVersion: 'v1',
	keycloak: false,
	idleReloadTimeout: 60000 * 15, // reload page after x miliseconds of inactivity, 0 to disable (default 15 minutes)
	thumbPresets: {}, // deprecated
	cache: {
		debug: false,
		enabled: process.env.NODE_ENV == 'development' ? false : true,
		refresh: {
			currency: 1440,
			endpoints: 1440,
			info: 1440,
			labels: 5,
			menus: 5,
			redirects: 5,
			rotators: 5,
			routes: 5,
			token: 1440,
			newsletter: 1440,
			gdprTemplate: 1440,
			thumbs: 5,
			cmsPages: 10,
			catalogCategories: 5,
			catalogLists: 5,
			catalogManufacturers: 5,
			catalogProduct: 5,
			catalogProducts: 5,
			catalogSellers: 5,
			faqCategories: 5,
			faqQuestions: 5,
			faqQuestion: 5,
			publishCategories: 5,
			publishPost: 5,
			publishPosts: 5,
			publishAuthors: <AUTHORS>
			locationPoints: 5,
			locationPoint: 5,
			events: 5,
			event: 5,
			staticContent: 5,
		},
	},
	facebook: {
		pixel: {
			env: ['production'],
			apiKey: '',
			target: 'head',
			gdpr: 'analytics',
		},
		conversionApi: {
			env: ['production'],
			debug: false,
			eventTestCode: '',
			events: [], // 'pageView', 'search', 'contact', 'viewContent', 'addToWishlist', 'addToCart', 'addPaymentInfo', 'completeRegistration', 'customizeProduct', 'findLocation', 'initiateCheckout', 'subscribe', 'purchase'
			gdpr: 'analytics',
			/*
				pageView: {
					name: 'PageView',
					data: ['title'],
				},
			*/
		},
	},
	google: {
		gtm: {
			env: ['production'],
			gdpr: false,
		},
		tracking: {
			gdpr: 'analytics',
			debug: false,
			affiliation: '',
			events: [], // 'gdprConsents', 'pageView', 'viewItem', 'viewItemList', 'selectItem', 'viewCart', 'removeProduct', 'addProduct', 'login', 'beginCheckout', 'addShippingInfo', 'addPaymentInfo', 'purchase', 'refund', 'addToWishlist', 'viewPromotion', 'selectPromotion'
			/*
				hooks: {
					mapProduct(data) {},
				},
				pageView: {
					name: 'page_view',
					hooks: {
						mapData(data) {},
					},
				},
			*/
		},
		remarketing: {
			env: ['production'],
			gdpr: 'marketing',
			debug: false,
			events: [], // 'home', 'searchresults', 'category', 'offerdetail', 'conversionintent', 'conversion', 'other'
			/*
			offerdetail: {
				data: [], // pagetype, id, code, total
			},
			*/
		},
		ga4: {
			env: ['production'],
			gdpr: 'analytics',
		},
	},
	hapi: {
		tracking: {
			debug: false,
			events: [], // catalogproduct, catalogcategory, cataloglist, catalogmanufacturer, publish, publishcategory, page
		},
	},
	/*
		compare: {
			limit: 5 // limit number of items in compare list
		},
		layouts: [],
		structuredData: {
			basic: {
				name: 'Site Name',
				organizationName: 'Company Name',
				logo: '',
				phone: '',
				email: '',
				socialNetworkUrls: '',
			},
		},
	*/
	fetchApi: {
		put: {
			appendLang: true,
		},
		post: {
			appendLang: true,
		},
	},
	catalog: {
		updatePriceQty: false, // If there are user specific prices, fetch and update products price and qty
		quickOrder: false, // enable/disable quick order template
		productsResponseFields: [], // global response fields for products
		listsResponseFields: [], // global response fields for lists
		manufacturersResponseFields: [], // global response fields for manufacturers
		/*
		hooks: {
			mapProduct: product => {} // callback function to modify product detail data once fetched
		}
		*/
	},
	publish: {
		postsResponseFields: [], // global response fields for posts
	},
	auth: {
		dashboard: null, // redirect from user dashboard to different page. For example list of orders - 'auth_my_webshoporder',
		loggedOutRedirect: 'auth_login', // redirect user to this page if logged out and trying to access auth page. "appUrls" key needs to be used.
		loggedInRedirect: 'auth', // redirect user to this page if logged in and trying to access login or signup page. "appUrls" key needs to be used.
	},
	webshop: {
		auth: true, // enable/disable webshop auth
	},
	viewOrder: {
		orderNumber: '%order_number%', // order number format. For example '%order_number% / %order_id%'
		orderPaymentTransfer: true,
		itemEanCode: true,
		warehouse: {
			enabled: false, // show warehouse info on order page
			permission: null, // restrict access to warehouse info by user role (staff, superuser, developer)
			includeEmpty: true, // show warehouses with no items
		},
	},
	redirectIgnoredUrls: [
		'/ADMIN/cache.asp/',
		'/ADMIN/cache.asp/',
		'/Admin/eWebEditor/Sql.asp/',
		'/Admin/Image/sql.asp/',
		'/Admin/Image/Thumb.asp/',
		'/Admin/Images/cache.asp/',
		'/Admin/IMAGES/check.asp/',
		'/Admin/Images/Sql.asp/',
		'/Admin/Images/SqlIn.asp/',
		'/Admin/Images/Thumb.asp/',
		'/ADMIN/inc/Logout.asp/',
		'/Editor/editor/Include/C1ass.asp/',
		'/Images/cache.asp/',
		'/Images/img/cache.asp/',
		'/Images/SqlIn.asp/',
		'/Images/SqlIn.asp/',
		'/images/Thumb.asp/',
		'/Inc/Md5.asp/',
		'/Manage/Image/Thumb.asp/',
		'/Manage/Images/sql.asp/',
		'/Autodiscover/Autodiscover.xml/',
		'/wp-admin/',
		'/wp-login.php/',
		'/wordpress/wp-admin/',
		'/wp/wp-admin/',
		'/test/wp-admin/',
		'/old/wp-admin/',
		'/wp-admin/admin-ajax.php/',
		'/wordpress/',
		'/wp/',
		'/utility/convert/data/config.inc.php/',
		'/plus/mytag_js.php/',
		'/xmlrpc.php/',
		'/admin/fckeditor/editor/filemanager/connectors/asp/connector.asp/',
		'/admin/fckeditor/editor/filemanager/connectors/asp/connector.aspx/',
		'/admin/Cms_Wysiwyg/directive/',
		'/api/xmlrpc/index/',
		'/wp-includes/wlwmanifest.xml/',
		'/wlwmanifest.xml/',
		'/apple-app-site-association/',
		'/favicon/favicon.ico/',
		'/remote/login/',
		'/Temporary_Listen_Addresses/',
		'/actuator/gateway/routes/',
		'/files/',
		'/laravel/.env/',
	],
});
