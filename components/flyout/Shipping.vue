<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel v-if="content.id == 's'" code="item_delivery_standard_delivery" />
			<BaseCmsLabel v-if="content.id == 'e'" code="item_delivery_express_delivery" />
			<BaseCmsLabel v-if="content.id == 'bb'" code="item_delivery_bigbang_delivery" />
			<BaseCmsLabel v-if="content.id == 'bb_xxl'" code="item_delivery_bigbang_xxl_delivery" />
			<BaseCmsLabel v-if="content.id == 'bb_fast'" code="item_delivery_premium" />
		</template>
		<template v-if="content?.shipping_options?.length">
			<template v-for="(options, index) in content.shipping_options" :key="options.id">
				<div class="shipping-row" v-if="options.id != 'p'">
					<div class="shipping-desc">
						<template v-if="content.status == '5'">
							<span v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(content.date))"></span>
						</template>
						<template v-else>
							<template v-if="options.id == 'bb_fast' && options.fast_shipping_titles?.length">
								<span v-html="labels.get('item_delivery_premium').replace('%s%', options.fast_shipping_titles?.join(''))"></span>
							</template>
							<template v-else>
								<span v-if="new Date(options.min_delivery_date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', content.shipping_dates[index])"></span>
								<span v-else-if="new Date(options.min_delivery_date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', content.shipping_dates[index])"></span>
								<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', content.shipping_dates[index])"></span>
							</template>
						</template>
					</div>
					<div v-if="options.id == 's'" v-html="labels.get('item_delivery_standard_delivery_flyout_desc')"></div>
					<div v-else-if="options.id == 'e'" v-html="labels.get('item_delivery_express_delivery_flyout_desc')"></div>
					<div v-else-if="options.id == 'bb'" v-html="labels.get('item_delivery_bigbang_delivery_flyout_desc')"></div>
					<div v-else-if="options.id == 'bb_xxl'" v-html="labels.get('item_delivery_bigbang_xxl_delivery_flyout_desc')"></div>
					<div v-else-if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium_flyout_desc')"></div>
				</div>
			</template>
		</template>
	</FlyoutLayout>
</template>

<script setup>
	const modal = useModal();
	const content = computed(() => modal.get('flyout')?.content || null);
	const labels = useLabels();
	const {formatDate} = useText();

	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
</script>

<style scoped lang="less">
.shipping-desc{
	padding: 0 0 20px; position: relative; display: flex; gap: 10px; align-items: center;
	&:before{.icon-clock(); font: 17px/1 var(--fonti);}
}
</style>