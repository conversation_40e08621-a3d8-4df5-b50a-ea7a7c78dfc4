<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel v-if="mode == 'service'" code="services_flyout_title" />
			<BaseCmsLabel v-else-if="mode == 'insurance'" code="insurances_flyout_title" />
		</template>
		
		<template #content>
			<div v-if="services?.length" class="cd-flyout-service-content" v-interpolation>		
				<div class="service" v-for="service in services" :key="service.id">
					<input v-if="service.category_type == 's'" class="special" v-model="currentlySelectedInsurance" type="radio" name="service" :value="service.id" :id="'service-' + service.id" />
					<input v-else class="special" v-model="currentlySelectedServices" type="checkbox" name="service" :value="service.id" :id="'service-' + service.id" />
					<label :for="'service-' + service.id">
						<span class="title">{{ service.title }}</span>
						<span class="price"><BaseUtilsFormatCurrency :price="service.price" /></span>
						<div class="desc" v-if="service.description" v-html="service.description"></div>
					</label>
				</div>
				<div class="service" v-if="mode == 'insurance'">
					<input class="special" v-model="currentlySelectedInsurance" type="radio" name="service" value="" id="service-0" />
					<label for="service-0">
						<span class="title"><BaseCmsLabel code="no_warranty" /></span>
					</label>
				</div>
			</div>
		</template>

		<template #footer>
			<button class="btn btn-outline btn-icon" @click="modal.close()"><BaseCmsLabel code="return_to_product" /></button>
			<button class="btn" @click="updateService()" :disabled="loading">
				<UiLoader mode="dots" v-if="loading" />
				<BaseCmsLabel v-if="!loading" code="flyout_close_confirm" />
			</button>
		</template>
	</FlyoutLayout>
</template>

<script setup>
	const modal = useModal();
	const webshop = useWebshop();
	const mode = computed(() => modal.get('flyout')?.mode || null);
	const content = computed(() => modal.get('flyout')?.content || null);
	const labels = useLabels();

	const currentlySelectedServices = ref([]);
	const currentlySelectedInsurance = ref(null);
	const loading = ref(false);

	const services = computed(() => {
		if(mode.value == 'insurance') return content.value?.insurances?.available || [];
		if(mode.value == 'service') return content.value?.services?.available || [];
	});

	watch(content, (newValue, oldValue) => {
		if(!newValue) return;
		currentlySelectedServices.value = newValue?.services?.selected?.length ? newValue?.services?.selected.map(el => el.id) : [];
		currentlySelectedInsurance.value = newValue?.insurances?.selected?.length ? newValue?.insurances?.selected[0].id : "";
	}, {immediate: true});

	async function updateService() {
		loading.value = true;
		let data = [];

		if(currentlySelectedInsurance.value) data.push(currentlySelectedInsurance.value);
		if(currentlySelectedServices.value?.length) currentlySelectedServices.value.forEach(el => data.push(el));
		
		await webshop.updateProduct([{
			shopping_cart_code: content.value.shopping_cart_code,
			quantity: content.value.quantity,
			services: data.length ? data : [''],
		}])
		loading.value = false;
		modal.close();
	};	
</script>

<style scoped lang="less">
	input[type=checkbox]+label, input[type=radio]+label{padding-left: 0; position: relative;}
	.title{display: block; padding-left: 30px; font-weight: bold; font-size: 16px; padding-right: 100px;}
	.desc{
		display: block; padding-top: 10px; font-size: 15px;
		:deep(p){padding: 0;}
	}
	.price{font-weight: bold; position: absolute; top: 0; right: 0; font-size: 16px;}
	.service{
		padding: var(--flyoutSideOffset); border-bottom: 1px solid var(--gray3);
		&:last-child{border-bottom: none;}
	}
</style>