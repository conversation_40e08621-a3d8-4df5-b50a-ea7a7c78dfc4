<template>
	<FlyoutLayout>
		<template #header>
			<BaseCmsLabel code="item_stores_flyout_title" tag="div" />
		</template>
		<div class="stores">
			<template v-if="content.warehouses_display?.length">
				<div v-for="item in content.warehouses_display" :key="item.id" class="store" :class="{'store-unavailable': item.available_qty <= 0}">
					<div class="indicator"></div>
					<div class="title">
						<NuxtLink v-if="item.url_without_domain" :to="item.url_without_domain" target="_blank">{{ (item.title2) ? item.title2 : item.title }}</NuxtLink>
						<template v-else>{{ (item.title2) ? item.title2 : item.title }}</template>
					</div>
				</div>
			</template>
		</div>
		<template #footer>
			<div class="store-legend">
				<div class="store-legend-item">
					<div class="indicator"></div>
					<div><BaseCmsLabel code="store_available" /></div>
				</div>
				<div class="store-legend-item store-legend-item-unavailable">
					<div class="indicator"></div>
					<div><BaseCmsLabel code="store_not_available" /></div>
				</div>
			</div>
		</template>
	</FlyoutLayout>
</template>

<script setup>
const modal = useModal()
const content = computed(() => modal.get('flyout')?.content || null);
</script>

<style lang="less" scoped>
.store{
	margin-bottom: 12px; display: flex; gap: 10px;
	&:last-child{margin-bottom: 0;}
}
.store-unavailable{
	color: var(--gray6);
	.indicator{background-color: #B6B6B6;}
}
.indicator{width: 12px; height: 12px; border-radius: 50%; background-color: #4CAF50; flex-grow: 0; flex-shrink: 0; margin-top: 5px;}
.title{font-size: 15px;}
.store-legend{
	display: flex; gap: 25px; color: var(--gray5); font-size: 12px;
	.indicator{margin: 0;}
}
.store-legend-item{display: flex; gap: 5px; align-items: center;}
.store-legend-item-unavailable{
	.indicator{background-color: #B6B6B6;}
}
</style>