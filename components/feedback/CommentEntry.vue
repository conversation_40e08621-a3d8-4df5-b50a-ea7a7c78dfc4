<template>
	<article class="comment">
		<div class="comment-header">
			<span class="comment-rate">
				<span v-for="i in 5" :key="i" class="icon-star-empty" :class="{'icon-star': i <= item.rate}"></span>
			</span>
			<span class="comment-purchase-confirmed"><BaseCmsLabel code='comment_purchase_confirmed' /></span>
			<span class="comment-date"><BaseUtilsFormatDate :date="item.datetime_created" format="DD/MM/YYYY" /></span>
		</div>

		<div class="comment-message">{{ item.message }}</div>
		<span class="comment-username" :class="{'comment-username-manager': item.manager > 0}">{{ item.display_name }}</span>

		<BaseFeedbackCommentReview :comment="item" v-slot="{status, rateUp, rateDown, positiveReviews, negativeReviews}">
			<div class="comment-review" :class="{'disabled': !item.can_reviews}">
				<template v-if="!status && item.can_reviews">
					<div class="review-btn review_up" :class="{'active': positiveReviews > 0}" @click="rateUp">{{ positiveReviews }}</div>
					<div class="review-btn review_down" :class="{'active': negativeReviews > 0}" @click="rateDown">{{ negativeReviews }}</div>
				</template>
				<span v-if="status" class="comment-review-success"><BaseCmsLabel :code='status' /></span>
			</div>
		</BaseFeedbackCommentReview>

		<!-- FIXME INTEG podatak?
		<div class="comment-source">Izvorno objavljeno na Samsung HR</div>
		-->
	</article>
</template>

<script setup>
	const props = defineProps(['item']);
</script>

<style lang="less" scoped>
	.comment{display: block; height: 100%; break-inside: avoid; margin-bottom: 12px; padding: 12px; background: var(--white); border: 1px solid #B6B6B6; border-radius: 12px;}
	.comment-header{display: flex; align-items: center;}
	.comment-rate{
		display: flex; align-items: center;
		:deep(.icon-star-empty){
			display: inline-block; position: relative; width: 15px; height: 15px; margin-right: 2px; opacity: 1!important;
			&:after{.pseudo(15px,15px); background: url(assets/images/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
			
		}
		:deep(.icon-star){
			width: 16px; height: 16px;
			&:after{width: 16px; height: 16px; background: url(assets/images/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
		}
	}
	.comment-purchase-confirmed{margin-left: 8px; font-size: 12px; font-weight: 600; color: #1FB549;}
	.comment-date{flex-grow: 1; font-size: 12px; color: var(--gray5); text-align: right;}
	.comment-message, .comment-username{display: block; margin-top: 16px; font-size: 14px; color: var(--gray5);}

	.comment-review{
		display: flex; align-items: center; margin-top: 16px;
		&.disabled{opacity: 0.6; pointer-events: none;}
	}
	.comment-review-success{font-size: 12px; color: var(--gray5);}
	.review-btn{
		display: flex; align-items: center; margin-right: 12px; padding-left: 22px; font-size: 12px; color: var(--black); position: relative; cursor: pointer;
		&:before{.icon-like(); font: 16px/1 var(--fonti); color: var(--black); position: absolute; top: -2px; left: 0;}
		&.active{
			color: var(--blue);
			&:before{color: var(--blue);}
		}
	}
	.review_down:before{top: 1px; .rotate(180deg);}
	.comment-source{margin-top: 16px; font-size: 10px; font-weight: 600; color: var(--gray5);}
</style>