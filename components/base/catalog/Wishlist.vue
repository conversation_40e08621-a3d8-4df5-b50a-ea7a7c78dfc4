<template>
	<slot :data="data" :items="items" :counter="counter" :categories="categories" :onRemove="onRemove" :loading="loading" :loadMore="loadMore" />
</template>

<script setup>
	const catalog = useCatalog();
	const {generateThumbs} = useImages();
	const props = defineProps({
		thumbPreset: String,
	});
	const loading = ref(false);
	const emit = defineEmits(['loadProductsWidget']);
	const data = ref(false);
	const items = ref();
	const wishlistId = ref();
	const counter = ref(0);
	const categories = ref([]);

	onMounted(async () => {
		if (!items.value) {
			loading.value = true;
			const res = await catalog.fetchWishlistProducts();
			data.value = res.data;
			items.value = res.data?.items;
			wishlistId.value = res.data?.id;
			loading.value = false;
		}

		// emit event with initial list
		emit('loadProductsWidget', {items: items.value});
	});

	// process items when data changes
	watch(
		() => catalog.getWishlistProducts(),
		async (newItems, oldItems) => {
			items.value = processItems(newItems?.items);
			data.value = newItems;

			// generate thumbs
			if (items?.value?.length && props.thumbPreset) {
				await generateThumbs({
					data: items.value,
					preset: props.thumbPreset,
				});
			}
		},
		{immediate: true}
	);

	function processItems(items) {
		if (!items?.length) return [];

		const products = [...items];
		counter.value = products?.length;

		// if there are no products return empty array
		if (!products?.length) return [];

		return products;
	}

	// remove all items from wishlist and refetch products
	async function onRemove() {
		loading.value = true;
		await catalog.removeWishlistProducts(wishlistId.value);

		setTimeout(async () => {
			const res = await catalog.fetchWishlistProducts();
			data.value = res.data;
			items.value = res.data?.items;
			wishlistId.value = res.data?.id;

			loading.value = false;
		}, 2000);
	}

	// fetch more items
	async function loadMore() {
		if (!data.value.pagination?.page?.next) return;

		loading.value = true;
		const res = await catalog.fetchWishlistProducts({to_page: data.value.pagination?.page?.next});
		data.value = res.data;
		wishlistId.value = res.data?.id;
		items.value = processItems(res.data?.items);
		loading.value = false;
	}
</script>
