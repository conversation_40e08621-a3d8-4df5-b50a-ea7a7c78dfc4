<template>
	<!-- FIXME maknut hardcodirane vrijednosti -->
	<BaseWebshopCartErrors v-slot="{errors, errorsItems, warnings}">
		<div class="unavailable-items" v-if="errors || errorsItems || warnings">
			<!--
			<div class="unavailable-item">
				<div class="title">Lorem ipsum dolor sit amet.</div>
			</div>
			<div class="unavailable-item">
				<div class="title">Lorem ipsum dolor sit amet.</div>
				<div class="btns">
					<span><BaseCmsLabel code="close" /></span>
				</div>
			</div>
			<div class="unavailable-item">
				<div>
					<div class="title">
						<BaseCmsLabel code="cart_error_price_changed" />
					</div>
					<div class="cnt">
						Lorem ipsum dolor sit amet.
						<div class="price">
							<span class="line-through">100,00€</span>
							50,00€
						</div>
					</div>
				</div>
				<div class="btns">
					<div class="btn-edit-items" @click="priceConfirm()">
						<span><BaseCmsLabel code="cart_error_accept_new_price" /></span>
					</div>
				</div>
			</div>
			-->

			<template v-if="items?.cart?.length">
				<div v-for="cart_error in items.cart" :key="cart_error.index" class="unavailable-item">
					<div class="title"><BaseCmsLabel :code="cart_error.label_name" tag="strong" /></div>
				</div>
			</template>
			<template v-if="warnings?.cart?.length">
				<div v-for="cart_warning in warnings.cart" :key="cart_warning.index" class="unavailable-item">
					<div class="title"><BaseCmsLabel :code="cart_warning.label_name" tag="strong" /></div>
					<div class="btns" @click="closeWarning()">
						<span><BaseCmsLabel code="close" /></span>
					</div>
				</div>
			</template>
			<template v-if="data?.length">
				<template v-for="items in data" :key="items.number">
					<template v-for="item in items.items" :key="item.id">
						<div v-for="error in item.errors" :key="error.index" class="unavailable-item">
							<div>
								<div class="title">
									<template v-if="error.label_name == 'error_price_changed'"><BaseCmsLabel code="cart_error_price_changed" /></template>
									<template v-else-if="error.qty == 0"><BaseCmsLabel code="cart_error_qty_empty" /></template>
									<template v-else-if="error.label_name == 'error_insurances_unavailable'">
										<BaseCmsLabel code="cart_error_insurances_unavailable" />
									</template>
									<template v-else><BaseCmsLabel code="cart_error_qty" /></template>
								</div>
								<div class="cnt">
									<template v-if="item.item.title.length <= 20">{{ item.item.title }}</template>
									<template v-else>{{ item.item.title.substring(0,20)+"..." }}</template>
									<span v-if="error.label_name == 'error_price_changed'" class="price">
										<span class="line-through"><BaseUtilsFormatCurrency :price="error.old_price" /></span>
										<BaseUtilsFormatCurrency :price="error.new_price" />
									</span>
								</div>
							</div>

							<div v-if="error.label_name == 'error_price_changed'" class="btns">
								<div class="btn-edit-items" @click="priceConfirm()">
									<span><BaseCmsLabel code="cart_error_accept_new_price" /></span>
								</div>
								<div class="btn-edit-items" @click="webshop.removeProduct([{shopping_cart_code: item.shopping_cart_code}])">
									<span><BaseCmsLabel code="remove" /></span>
								</div>
							</div>
							<div v-else-if="error.qty == 0" class="btns" @click="webshop.removeProduct([{shopping_cart_code: item.shopping_cart_code}])">
								<span><BaseCmsLabel code="remove" /></span>
							</div>
							<div v-else @click="scrollTo('#position-' + item.item.code, {offset: 90})" class="btns">
								<span><BaseCmsLabel code="cart_edit_qty" /></span>
							</div>
						</div>
					</template>
				</template>
			</template>
		</div>
	</BaseWebshopCartErrors>
</template>

<script setup>
	const props = defineProps(['data', 'items', 'warnings']);
	const webshop = useWebshop();
	const endpoints = useEndpoints();
	const { checkoutLoading } = useCheckout();
	const { scrollTo } = useDom();

	async function closeWarning() {
		checkoutLoading.value = true;

		let shopping_cart_codes = []
		props.warnings.cart.forEach(el => {
			el.shopping_cart_codes.forEach(el2 => {
				shopping_cart_codes.push({'shopping_cart_code':el2});
			})
		})

		await useApi(`${endpoints.get('_delete_hapi_webshop_product')}`, {
			method: 'DELETE',
			body: shopping_cart_codes
		}).then(async res => {
			await webshop.fetchCart()
		});

		checkoutLoading.value = false;
	}

	async function priceConfirm() {
		checkoutLoading.value = true;

		let cartCode = {}
		props.data.forEach(el => {
			el.items.forEach(el2 => {
				cartCode = el2.shopping_cart_code;
			})
		})

		let itemNewPrice = {};
		props.data.forEach(el => {
			el.items.forEach(el2 => {
				if(el2.errors){
					el2.errors.forEach(el3 => {
						itemNewPrice = el3.new_price;
					})
				}

			})
		})

		await useApi(`${endpoints.get('_post_hapi_webshop_accept_new_product_price')}`, {
			method: 'POST',
			body: {"shopping_cart_code": cartCode, "accept_price": itemNewPrice}
		}).then(async res => {
			if(res.success) {
				await webshop.fetchCart()
			}
		});

		checkoutLoading.value = false;
	}
</script>

<style lang="less" scoped>
	.unavailable-items{display: flex; flex-direction: column; gap: 15px; margin-bottom: 25px;}
	.unavailable-item{
		position: relative; padding: 15px; display: flex; gap: 10px; font-weight: bold; margin-right: auto; background: #fff; border-radius: var(--borderRadius); width: 100%; border: 1px solid var(--errorColor); color: var(--textColor); font-size: 15px; line-height: 1.35; text-align: left;
		&:before{.icon-close(); font: 23px/1 var(--fonti); color: var(--errorColor); position: relative;}
	}
	.btns{cursor: pointer; margin-left: auto; font-weight: normal; text-decoration: underline; color: var(--errorColor);}
	.title{font-size: 18px;}
	.cnt{width: 100%; font-weight: normal; padding: 5px 0 0;}
	.price{
		padding-top: 5px;
		&>span{padding-right: 5px;}
	}
</style>
