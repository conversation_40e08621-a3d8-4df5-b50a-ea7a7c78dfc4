<template>
	<BaseWebshopCouponForm v-slot="{onSubmit, onRemove, couponCode, message, error, loading, activeCoupon, handleInput}">
		<div class="coupons">
			<form @submit.prevent="couponCode && !activeCoupon && onSubmit()">
				<div class="title"><BaseCmsLabel code="coupon_enter_code" /></div>
				<div class="coupon-form">
					<input class="base-coupons-input" :readonly="activeCoupon?.code" :placeholder="activeCoupon?.code ? activeCoupon.code : labels.get('coupon_code')" type="text" :value="couponCode" name="coupon" @keyup="handleInput" />
					<button v-if="!activeCoupon" :disabled="!couponCode || loading" type="submit">
						<UiLoader mode="dots" v-if="loading" />
						<BaseCmsLabel code="coupon_add" v-else />
					</button>
					<button v-if="activeCoupon" type="button" @click="onRemove">
						<UiLoader mode="dots" v-if="loading" />
						<BaseCmsLabel code="coupon_remove" v-else />
					</button>
				</div>
				<BaseCmsLabel v-if="message" class="status" tag="div" :code="message" />

				<!--
				<div class="coupons-list">
					<div class="coupons-list-title"><BaseCmsLabel code="coupon_available" /></div>
					<table cellspacing="0" cellpadding="0" class="coupons-list-items">
						<thead>
							<th>Vrijednost</th>
							<th>Promo kod</th>
							<th></th>
						</thead>
						<tbody>
							<tr>
								<td class="coupon-item-value"><span>-30%</span></td>
								<td class="coupon-item-title">test</td>
								<td class="coupon-item-btn"><span><BaseCmsLabel code="coupon_remove" /></span></td>
							</tr>
							<tr>
								<td class="coupon-item-value"><span>-30%</span></td>
								<td class="coupon-item-title">test</td>
								<td class="coupon-item-btn"><span><BaseCmsLabel code="coupon_remove" /></span></td>
							</tr>
						</tbody>
					</table>
				</div>
				-->
				
				<BaseAuthCoupons v-if="auth.isLoggedIn()" :only-active="true" :replace="true" v-slot="{items, onActivate, onDeactivate, activatedCoupons, loading}">
					<div v-if="items?.length" class="coupons-list">
						<h4 class="coupons-list-title"><BaseCmsLabel code="coupon_available" /></h4>
						<table cellspacing="0" cellpadding="0" class="coupons-list-items">
							<thead>
								<th><BaseCmsLabel code="coupon_value" /></th>
								<th><BaseCmsLabel code="coupon_code" /></th>
								<th></th>
							</thead>
							<tbody>
								<tr v-for="item in items" :key="item.id">
									<td class="coupon-item-value">
										<span v-if="item.type == 'f'"><BaseUtilsFormatCurrency :price="item.coupon_price" /></span>
										<span v-else>-{{ item.coupon_percent * 100 }}%</span>
									</td>
									<td class="coupon-item-title"><span>{{item.code}}</span></td>
									<td class="coupon-item-btn">
										<span v-if="activatedCoupons.includes(item.code)" @click="onDeactivate(item.code)"><UiLoader mode="dots" class="dark" v-if="loading" /><BaseCmsLabel code="coupon_remove" v-else /></span>
										<span v-else @click="onActivate(item.code)"><UiLoader mode="dots" class="dark" v-if="loading" /><BaseCmsLabel code="coupon_use" v-else /></span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</BaseAuthCoupons>
			
			</form>
		</div>
	</BaseWebshopCouponForm>	
</template>

<script setup>
	const labels = useLabels();
	const auth = useAuth();
</script>

<style lang="less" scoped>
	.coupons{padding: 20px 0 20px;}
	.title{font-size: 15px; font-weight: bold; padding: 0 0 10px;}
	.coupon-form{display: flex; height: 48px;}
	input{border: 1px solid var(--gray5); border-radius: 8px 0 0 8px; border-right: 0; flex-grow: 1; height: 100%; font-size: 16px;}
	button{border-radius: 0 8px 8px 0; flex: 0 0 130px; height: 100%; padding: 0; display: flex; align-items: center; justify-content: center;}
	.status{font-size: 12px; padding: 6px 0 0 12px;}
	
	.coupons-list-items{
		width: 100%;
		thead{text-align: left;}
		th{font-weight: normal; color: var(--gray5); padding-bottom: 5px;}
		td{padding: 5px 0;}
	}
	.coupons-list{padding: 20px 0 0; font-size: 12px;}
	.coupons-list-title{padding-bottom: 20px; font-size: 15px;}
	.coupon-item-value{
		padding: 0 0 15px; font-size: 12px;
		span{display: inline-flex; background: var(--green); border-radius: 3px; color: #fff; padding: 2px 5px;}
	}
	.coupon-item-btn{
		margin-left: auto; text-align: right; color: var(--blueDark);
		span{text-decoration: underline; text-decoration-thickness: 1px; text-underline-offset: 2px; cursor: pointer;}
	}
	:deep(.dots-loader){justify-content: flex-end;}
</style>
