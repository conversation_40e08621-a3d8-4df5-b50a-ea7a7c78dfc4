<template>
	<BaseCmsPage v-slot="{page}">
		<BaseWebshopCart thumb-preset="cart" v-slot="{cart, parcels, counter}">
			<div class="wrapper">
				<h1 v-html="page?.seo_h1"></h1>
			</div>
			<ClientOnly>
				<div class="wrapper" v-if="parcels?.length">
					<div class="w-row" v-if="parcels?.length">
						<div class="w-col1">
							<WebshopErrorMessage :data="parcels" :items="cart?.errors" :warnings="cart?.warnings" />
							<div class="carts" v-if="parcels?.length">
								<div class="cart" v-for="parcel in parcels" :key="parcel.number">
									<div class="cart-title">
										{{ parcel.number }}. <span class="cart-parcel"><BaseCmsLabel code="seller_item_title" />&nbsp;</span>
										<template v-if="parcel.parcel_name">{{ parcel.parcel_name }}</template>
									</div>
									<div class="cart-items">
										<WebshopCartItem v-for="item in parcel.items" :data="item" :parcel="parcel" :key="item.id" />	
									</div>
								</div>
							</div>

							<!--
							<BaseCatalogLists :fetch="{code: 'cart_list', single: true}" v-slot="{items}">
								<template v-if="items?.length">
									<div class="cart-sl related" v-for="list in items" :key="list.id">
										<div class="related-title"><BaseCmsLabel code="cart_related_products" /></div>
										<BaseCatalogProductsWidget :fetch="{mode: 'widget', list_code: list.code, sort: 'list_position', only_available: true, limit: 9}" v-slot="{items}">
											<div class="cart-sl-items related-items">
												<BaseUiSwiper
													name="cart-products"
													:options="{
														breakpoints: {
															0: {
																enabled: false
															},
															980: {
																slidesPerView: 2,
																slidesPerGroup: 2,
																enabled: true
															},
															1600: {
																slidesPerView: 4,
																slidesPerGroup: 4,
																enabled: true
															}
														}
													}">
													<BaseUiSwiperSlide v-for="item in items" :key="item.id">
														<CatalogIndexEntry :item="item" mode="cart" />
													</BaseUiSwiperSlide>
												</BaseUiSwiper>
											</div>
										</BaseCatalogProductsWidget>
									</div>
								</template>
							</BaseCatalogLists>
							-->
						</div>

						<div class="w-col2">
							<WebshopSidebar />
							<!--
							<WebshopWidgetFixedbar mode="cart" />
						-->
						</div>
					</div>
				</div>
				<LazyWebshopEmptyCart v-if="!parcels?.length" />
				<template #fallback>
					<UiLoader class="spacing" />
				</template>
			</ClientOnly>
		</BaseWebshopCart>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	.wrapper{padding: 25px 0;}
	.w-row{display: flex; gap: 25px;}
	.w-col1{flex: 1;}
	.w-col2{flex: 0 0 460px;}
	h1{font-size: 24px; font-weight: bold; padding: 0 0 30px;}

	.carts{display: flex; flex-direction: column; gap: 20px;}
	.cart{background: #fff; border-radius: var(--borderRadius); padding: 20px 15px 25px;}
	.cart-title{font-size: 18px; font-weight: bold; padding: 0 0 25px;}
</style>