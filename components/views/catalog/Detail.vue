<template v-if="item?.id">
	<BaseMetaSeo :data="item" />
	<CmsBreadcrumbs v-if="item?.breadcrumbs" :items="item.breadcrumbs" class="cd-bc wrapper" />
	<Body :class="{'age-verification': item.xxx && !isAdult}" />
	<div class="cd-main wrapper">
		<div class="cd-col1">
			<!-- images -->
			<CatalogProductImages :item="item" />

			<!-- attributes -->
			<div v-if="displayedAttributes" class="cd-tab cd-tab-specs">
				<BaseCmsLabel code='tab_specs' tag="div" class="cd-tab-title" :class="{'active': toggleAttrEl}" @click="toggleAttrContainer" />
				<template v-if="toggleAttrEl">
					<div class="cd-tab-content cd-tab-attributes">
						<div v-for="(item, key) in displayedAttributes" :key="key" class="cd-tab-attribute">
							<div class="cd-tab-attribute-title">{{ item.title }}</div>
							<div class="cd-tab-attribute-desc">
								{{ item.values.join(', ') }}
								<template v-if="item.unit"> {{ item.unit }}</template>
							</div>
						</div>
					</div>
					<div v-if="Object.keys(itemsAttributes).length > 6" class="cd-tab-more cd-tab-more-attr" @click="toggleAttr">{{ showAttr ? labels.get('show_less') : labels.get('show_more') }}</div>
				</template>
			</div>

			<!-- description -->
			<div v-if="item?.content" class="cd-tab cd-tab-desc">
				<BaseCmsLabel code='tab_product_description' tag="div" class="cd-tab-title" :class="{'active': toggleDescEl}" @click="toggleDescContainer" />
				<div v-if="toggleDescEl" class="cd-tab-content cd-tab-content-desc">
					<div v-html="displayedContent" />
					<div v-if="item?.content?.length > 1000" class="cd-tab-more cd-tab-more-desc" @click="toggleDesc">{{ showDesc ? labels.get('show_less') : labels.get('show_more') }}</div>
				</div>
			</div>

			<!-- manufacturer description -->
			<div v-if="manufacturerContent" class="cd-tab cd-tab-desc">
				<BaseCmsLabel code='tab_product_manufacturer_desc' tag="div" class="cd-tab-title" :class="{'active': toggleManuDescEl}" @click="toggleManuDescContainer" />
				<div v-if="toggleManuDescEl" class="cd-tab-content cd-tab-content-desc">
					<div v-html="manufacturerContent" />
				</div>
			</div>

			<!-- comments -->
			<div v-if="item.feedback_comment_widget" class="cd-tab cd-tab-comment" ref="comments">
				<BaseCmsLabel code='tab_comments' tag="div" class="cd-tab-title" :class="{'active': toggleCommentEl}" @click="toggleCommentContainer" />
				<div v-if="toggleCommentEl" class="cd-tab-content cd-tab-content-comment">
					<ClientOnly>
						<div class="cd-comments-info">
							<BaseFeedbackRatesWidget :data="item.feedback_rate_widget" v-slot="{rate, stars}">
								<div class="cd-comments-info-col1">
									<div v-if="rate > 0" class="cd-comments-rate-average">{{ rate }} od 5</div>
									<div class="cd-rate-section">
										<span class="cd-rate" v-html="stars" />
										<div class="cd-rate-votes">{{ item.feedback_rate_widget.rates_votes }} <BaseCmsLabel code='review' /></div>
									</div>
									<button class="cd-comments-form-button" :class="{'active': commentsForm}" @click="commentsForm = !commentsForm, itemData()">
										<span v-if="!commentsForm" class="show"><BaseCmsLabel code='comments_show_form' /></span>
										<span v-else class="hide"><BaseCmsLabel code='comments_hide_form' /></span>
									</button>
								</div>
								<div v-if="rate > 0" class="cd-comments-info-col2 cd-chart-items">
									<div class="cd-chart-item" v-for="item in ratesChart" :key="item.rate">
										<div class="cd-chart-rate">{{ item.rate }}</div>
										<div class="cd-chart-bar"><span class="cd-chart-progress-bar" :style="{width: calculateWidth(item.counter)}"></span></div>
										<div class="cd-chart-qty">{{ item.counter }}</div>
									</div>
								</div>
							</BaseFeedbackRatesWidget>
						</div>
						<!-- FIXME INTEG nedostaje dizajn
						<div class="comments-form active" v-show="commentsForm">
							<BaseFeedbackCommentsForm class="form-comment form-animated-label" v-slot="{fields, onReset, status}">
								<template v-if="!status?.success">
									<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
										<p class="field comment-field" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel, 'hidden': item.type == 'hidden'}]">
											<BaseFormInput />
											<BaseCmsLabel tag="label" :for="item.name" :code="item.name == 'message' ?  'form_comments_message' : item.name" />
											<span class="error" v-show="errorMessage" v-html="errorMessage" />
										</p>
									</BaseFormField>
									<div class="comment-button-container">
										<button class="btn btn-send-comment" type="submit">{{ labels.get('send_comment') }}</button>
										<div class="comment-form-note">{{ labels.get('comment_form_note') }}</div>
									</div>
								</template>

								<div class="comment-success" v-if="status?.success">
									<div v-html="labels.get('comment_success')" />
									<a href="javascript:void(0);" @click="onReset">{{ labels.get('comment_add_new') }}</a>
								</div>
							</BaseFeedbackCommentsForm>
						</div>
						-->
						<BaseFeedbackComments :items="item.feedback_comment_widget.items" v-slot="{items: comments, loadMore, nextPage}">
							<div v-if="comments.length" class="cd-comments">
								<div class="cd-comment-items">
									<FeedbackCommentEntry v-for="comment in comments" :key="comment.id" :item="comment" />
								</div>
								<div class="cd-btn-container">
									<button v-if="nextPage" class="btn btn-outline cd-comments-more" @click="loadMore"><BaseCmsLabel code='comment_load_more' /></button>
								</div>
							</div>
						</BaseFeedbackComments>
					</ClientOnly>
				</div>
			</div>
		</div>

		<div class="cd-col2">
			<div class="cd-container">
				<!-- manufacturer -->
				<div v-if="item.manufacturer_title" class="cd-brand">
					<NuxtLink :to="item.manufacturer_url_without_domain">{{ item.manufacturer_title }}</NuxtLink>
				</div>

				<!-- title -->
				<h1 class="cd-title">{{ item.seo_h1 }}</h1>

				<!-- ratings/comments/code -->
				<div class="cd-info">
					<BaseFeedbackRatesWidget v-if="item?.feedback_rate_widget && item?.feedback_rate_widget?.rates_votes > 0" :data="item.feedback_rate_widget" v-slot="{stars}">
						<div class="cd-rate-section">
							<div v-if="mobileBreakpoint" class="cd-rate-average">{{ Number(item.feedback_rate_widget.rates).toFixed(1) }}</div>
							<span class="cd-rate" v-html="stars" />
							<div v-if="!mobileBreakpoint" class="cd-rate-average">({{ Number(item.feedback_rate_widget.rates).toFixed(1) }})</div>
							<div class="cd-rate-votes" @click="scrollToComments">{{ item.feedback_rate_widget.rates_votes }} <template v-if="!mobileBreakpoint"><BaseCmsLabel code='review' /></template></div>
						</div>
					</BaseFeedbackRatesWidget>
					<div class="cd-code"><BaseCmsLabel code='sku' />:{{ item.code }}</div>
				</div>

				<!-- price -->
				<div v-if="item?.price_custom > 0" class="cd-price">
					<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
						<div class="cd-current-price red"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom" /></div>
						<div class="cd-old-price line-through"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
						<span class="cd-price-info">
							<span class="cd-price-tooltip"><BaseCmsLabel code='lowest_price' /></span>
						</span>
					</template>
					<template v-else>
						<div class="cd-current-price"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom" /></div>
					</template>
				</div>
				<div v-if="installmentPrice" class="cd-price-installment" v-html="labels.get('installments_price_text').replace('%PRICE%', formatCurrency(installmentPrice))"></div>

				<!-- dynamic price -->
				<template v-if="item.is_available">
					<div v-if="item.price_custom_prices_cart && item.price_custom_prices_cart_expire" class="cd-conf-price-section">
						<div class="cd-conf-price-header">
							<BaseCmsLabel code="configured_price" tag="span" class="cd-conf-price-title" />
							<div class="cd-conf-price-remove" @click="dynamicPriceRemove(item)"><BaseCmsLabel code='remove' /></div>
						</div>
						<div class="cd-conf-price">
							<span class="cd-current-price red"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom_prices_cart" /></span>
							<div class="cd-conf-price-timer">
								<BaseCmsLabel code="conf_price_valid" />
								<span class="cd-conf-price-timer-bottom">
									<ClientOnly><span class="countdown" v-html="countdownFormatted"></span></ClientOnly>
								</span>
							</div>
						</div>
						<div v-if="labels.get('conf_price_note') != 'conf_price_note'" class="cd-conf-price-note"><BaseCmsLabel code="conf_price_note" tag="span" /></div>
					</div>
					<template v-else-if="item.extra_price_cart_dynamicprice != 0 && item.extra_price_dynamicprice != 0">
						<div class="cd-conf-price-label" :class="{'active': !dynamicPriceActive}">
							<span><BaseCmsLabel code="conf_price_label" />&nbsp;</span>
							<a href="javascript:void(0)" @click="dynamicPriceSelect(item)"><BaseCmsLabel code="conf_price" /></a>
						</div>
						<template v-if="item.extra_price_mode_dynamicprice">
							<div v-if="item.extra_price_mode_dynamicprice == 'show_form_our'" class="cd-conf-price-label" :class="{'active': dynamicPriceActive}"><BaseCmsLabel code="dynamicprice_desc_lowest_price" tag="span" /></div>
							<div v-else-if="item.extra_price_mode_dynamicprice == 'show_form_limit'" class="cd-conf-price-label" :class="{'active': dynamicPriceActive}"><BaseCmsLabel code="dynamicprice_desc_lowest_price" tag="span" /></div>
						</template>
					</template>
				</template>

				<!-- services -->
				<div v-if="item.is_available && item.services" class="cd-services">
					<div v-for="item in item.services" :key="item.code" class="cd-service" :class="[item.code]">
						<div class="cd-service-title" :class="[item?.code]">{{ item?.title }}</div>
						<div v-if="serviceBid" class="cd-service-bid" :class="[item?.code]">
							<template v-for="(item, index) in serviceBid[item?.code]" :key="index">
								<span class="title">{{ item?.title }} - </span>
								<span class="price"><BaseUtilsFormatCurrency :price="item.price" /></span>
							</template>
						</div>
					</div>
				</div>

				<!-- item status -->
				<template v-if="item.status">
					<div v-if="item.status == 1" class="cd-available-qty">
						<template v-if="(item.last_piece_sale != 0 && item.last_piece_sale) && item.warehouses_single_pickup_display"><BaseCmsLabel code='odprodaja' tag="span" class="available-last" /></template>
						<template v-else-if="item.last_piece_sale != 0 && item.last_piece_sale"><BaseCmsLabel code='odprodaja_2' tag="span" class="available-last" /></template>
						<template v-else><BaseCmsLabel code='na_zalogi' tag="span" /></template>
					</div>
					<div v-else-if="item.status == 2" class="cd-available-qty">
						<span v-if="item.availability_info">{{ labels.get('na_zalogi_dobavitelja').replace('%MIN_DAY%', item.availability_info.min_days).replace('%MAX_DAY%', item.availability_info.max_days) }}</span>
					</div>
					<div v-else-if="item.status == 4" class="cd-available-qty">
						<BaseCmsLabel code='na_zalogi_ena' tag="span" class="available-last" />
					</div>
					<div v-else-if="item.status == 5" class="cd-available-qty">
						<template v-if="item.is_available && item.date_available">
							<span>{{ labels.get('na_voljo') }} {{ item.date_available.includes('.') ? item.date_available : formatDate(item.date_available) }}</span>
						</template>
						<template v-else><BaseCmsLabel code='ni_na_zalogi_preorder' tag="span" class="unavailable" /></template>
					</div>
					<div v-else-if="item.status == 7" class="cd-available-qty">
						<BaseCmsLabel code='ni_na_zalogi' tag="span" class="unavailable" />
					</div>
					<div v-else-if="item.status == 9" class="cd-available-qty">
						<BaseCmsLabel code='dalj_ni_na_zalogi' tag="span" class="unavailable" />
					</div>
				</template>

				<template v-if="!item.temporary_unavailable">
					<template v-if="item.is_available">
						<div class="cd-add-to-cart">
							<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :disabled="item.xxx && !isAdult" v-if="item.is_available" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: 1, services: selectedServices}">
								<div v-if="loading" class="btn cd-btn-add btn-loader"><div></div><div></div><div></div></div>
								<div v-else class="btn cd-btn-add" @click="onAddToCart()"><BaseCmsLabel code='cd_add_to_shopping_cart' tag="span" /></div>
							</BaseWebshopAddToCart>
						</div>
					</template>
					<template v-else-if="item.feedback_notification_widget">
						<div class="cd-add-to-cart">
							<!-- FIXME INTEG dizajn?
							<FeedbackNotificationForm :itemStatus="item.status" />
							-->
						</div>
					</template>
				</template>
			</div>

			<!-- shipping info -->
			<div v-if="item.shipping_options && item.active_shipping_options_count" class="cd-item-info-shipping cd-container flyout" @click="flyoutOpen('shipping')">
				<div class="cd-container-title icon shipping"><BaseCmsLabel code="item_delivery_tab_title" /></div>
				<div class="cd-container-content">
					<template v-for="(options, index) in item.shipping_options" :key="options.id">
						<div v-if="options.id != 'p'" class="cd-shipping-desc">
							<template v-if="item.status == 5">
								<span v-if="options.id == 's'" v-html="labels.get('item_delivery_standard_delivery')"></span>
								<span v-else-if="options.id == 'e'" v-html="labels.get('item_delivery_express_delivery')"></span>
								<span v-else-if="options.id == 'bb'" v-html="labels.get('item_delivery_bigbang_delivery')"></span>
								<span v-else-if="options.id == 'bb_xxl'" v-html="labels.get('item_delivery_bigbang_xxl_delivery')"></span>
								<span v-else-if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium_title')"></span>&nbsp;<span v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(item.shipping_date))"></span>
							</template>
							<template v-else-if="item.status != 5">
								<span v-if="options.id == 's'" v-html="labels.get('item_delivery_standard_delivery')"></span>
								<span v-else-if="options.id == 'e'" v-html="labels.get('item_delivery_express_delivery')"></span>
								<span v-else-if="options.id == 'bb'" v-html="labels.get('item_delivery_bigbang_delivery')"></span>
								<span v-else-if="options.id == 'bb_xxl'" v-html="labels.get('item_delivery_bigbang_xxl_delivery')"></span>
								<span v-else-if="options.id == 'bb_fast'" v-html="labels.get('item_delivery_premium').replace('%s%', options.fast_shipping_titles?.join(''))"></span> <template v-if="options.id != 'bb_fast'">
									<span v-if="new Date(options.min_delivery_date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', shippingDate[index])"></span>
									<span v-else-if="new Date(options.min_delivery_date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', shippingDate[index])"></span>
									<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', shippingDate[index])"></span>
								</template>
							</template>
						</div>
					</template>
				</div>
			</div>

			<div v-if="item.status != 7 && item.shipping_options?.some(obj => obj.id === 'p')" class="cd-item-info-shipping cd-container flyout" @click="flyoutOpen('stores')">
				<div class="cd-container-title icon stores">
					<template v-if="item.status == '4' && item.warehouses_display"><BaseCmsLabel code="item_delivery_tab_title" /></template>
					<template v-else><BaseCmsLabel code="item_stores_tab_title" /> <template v-if="item.warehouses_display?.length">({{ item.warehouses_display?.filter(warehouse => warehouse.available_qty > 0).length }})</template></template>
				</div>
				<div class="cd-container-content">
					<template v-if="item.status == 5">
						<span v-if="item.warehouses_single_pickup_display?.title" v-html="labels.get('item_time_of_delivery_preorder_single').replace('%s%', item.warehouses_single_pickup_display.title).replace('%s2%', formatDate(item.shipping_date))"></span>
						<span v-else v-html="labels.get('item_time_of_delivery_preorder').replace('%s%', formatDate(item.shipping_date))"></span>
					</template>
					<template v-else-if="item.shipping_options?.some(obj => obj.id === 'p' && obj.active == true)">
						<span v-if="item.warehouses_single_pickup_display?.title" v-html="labels.get('item_delivery_pickup_single').replace('%s%', item.warehouses_single_pickup_display.title)"></span>
						<span v-else v-html="labels.get('item_delivery_pickup_store')"></span><span v-if="new Date(date * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('item_time_of_delivery_today').replace('%s%', pickupDate)"></span>
						<span v-else-if="new Date(date * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('item_time_of_delivery_tomorow').replace('%s%', pickupDate)"></span>
						<span v-else v-html="labels.get('item_time_of_delivery').replace('%s%', pickupDate)"></span>
					</template>
				</div>
			</div>

			<!-- sellers -->
			<div v-if="item.seller_id && firstOtherOffer && (!firstMainOffer || item.offers_other_total != 1)" class="cd-seller-item cd-container flyout" @click="flyoutOpen('seller', {'offers': item.offers, 'id': item.offer_id})">
				<div class="cd-container-title icon seller"><BaseCmsLabel code="tab_seller_items" /></div>
				<div class="cd-container-content">
					<div class="cd-seller-desc cd-flyout-btn" v-html="offersDescl"></div>
				</div>
			</div>

			<!-- energy box -->
			<div v-if="energyAttr" class="cd-energy-item cd-container flyout" @click="flyoutOpen('energy', item?.energy_image_upload_path_thumb_energy_gallery)">
				<div class="cd-container-title icon energy"><BaseCmsLabel code="energy_title" /></div>
				<div class="cd-container-content cd-energy-content">
					<BaseUiImage v-if="energyAttr.image_upload_path" :src="energyAttr.image_upload_path" width="40" height="22" default="/images/no-image-50.jpg" :alt="energyAttr.title" />
					<BaseCmsLabel code='energy_info' />
				</div>
			</div>
		</div>
		<CatalogAgeVerificationOverlay :item="item" class="cp-detail-age-verification" />
	</div>
	<ClientOnly>
		<Teleport to="body">
			<CatalogCompare />
		</Teleport>
	</ClientOnly>	
</template>

<script setup>
	const webshop = useWebshop();
	const config = useAppConfig();
	const endpoints = useEndpoints();
	const route = useRoute();
	const labels = useLabels();
	const {emit} = useEventBus();
	const {generateThumbs} = useImages();
	const {formatCurrency} = useCurrency();
	const {getLastUrlSegment} = useUrl();
	const item = useState('product');
	const {openAgeModal, isAdult} = useAgeVerification();

	onMounted(async () => await mounted());
	watch(item, async () => await mounted());

	let mountedTimeout;
	async function mounted() {
		clearInterval(updateDynamicPriceTimer);

		//services
		watchServices();
		serviceUpdate();

		//dynamic price countdown
		dynamicPriceDate.value = item?.value?.price_custom_prices_cart_expire;
		if (dynamicPriceDate.value) {
			setInterval(updateDynamicPriceTimer, 1000);
		}

		//4th vision
		useHead({
			script: [
				{
					src: config.host == 'https://www.bigbang.si' ? labels.get('prod_3dviewer') : labels.get('dev_3dviewer'),
					type: 'module',
					id: '4thvision',
					async: true,
				},
			],
		});
	}

	onUnmounted(() => {
		clearInterval(updateDynamicPriceTimer);
		removeScript();
	});

	//rwd
	const {mobileBreakpoint} = inject('rwd');

	//remove script
	function removeScript() {
		// Find the script by id and remove it
		const script = document.getElementById('4thvision');
		if (script) {
			script.remove();
		}
	}

	// generate thumbnails
	async function imageThumbs() {
		return Promise.all([
			generateThumbs({
				data: item.value,
				preset: 'catalogDetail'
			}),
			generateThumbs({
				data: item.value,
				preset: 'catalogEnergy'
			}),
		]);
	}

	//installments
	const installmentPrice = computed(() => {
		if (item?.value?.installments_calculation && item?.value?.installments_calculation?.regular) {
			const values = Object.values(item.value?.installments_calculation.regular);
			return Math.min(...values);
		}
	});

	//attributes
	let showAttr = ref(false);
	let toggleAttrEl = ref(true);

	function toggleAttrContainer() {
		toggleAttrEl.value = !toggleAttrEl.value;
	}

	const itemsAttributes = computed(() => {
		let itemsAttributes = {};
		if(item?.value?.attributes){
			item?.value?.attributes.forEach(itemAttribute => {
				if(!['attribute_badges', 'superbenefits', 'katalog-9001'].includes(itemAttribute.code)) {
					const key = itemAttribute.attribute_title;
					const value = itemAttribute.title;
					const unit = itemAttribute.attribute_unit;

					if (!itemsAttributes[key]){itemsAttributes[key] = { values: [], unit: unit || '' };}

					itemsAttributes[key].values.push(value);
				}
			});
		} else {
			itemsAttributes = null;
		}

		return itemsAttributes;
	});

	const displayedAttributes = computed(() => {
		const attributesArray = Object.entries(itemsAttributes.value || {});
		if (showAttr.value) {
			return attributesArray.map(([title, value]) => ({ title, ...value }));
		} else {
			return attributesArray.slice(0, 6).map(([title, value]) => ({ title, ...value }));
		}
	});

	function toggleAttr() {
		showAttr.value = !showAttr.value;
	}

	//desc
	let toggleDescEl = ref(true);
	let showDesc = ref(false);

	const displayedContent = computed(() => {
		if (showDesc.value == true) {
			return item?.value?.content;
		} else {
			return item?.value?.content?.substring(0, 1000) + (item.value.content.length > 1000 ? '...' : '');
		}
	});

	function toggleDescContainer() {
		toggleDescEl.value = !toggleDescEl.value;
	}
	function toggleDesc() {
		showDesc.value = !showDesc.value;
	}

	//manufacturer content
	let toggleManuDescEl = ref(false);
	function toggleManuDescContainer() {
		toggleManuDescEl.value = !toggleManuDescEl.value;
	}

	let flixContent = ref(null);
	if(item?.value?.check_lists?.webcatalog_157270 == true) {
		try{
			flixContent.value = await $fetch(`https://media.flixcar.com/delivery/webcall/match/10251/sl/ean/${item.value.ean_code}?resp=0`);
		} catch (e) {
			flixContent.value = null;
		}
	}
	watch(
		() => item.value,
		async (newValue) => {
			if(item?.value?.check_lists?.webcatalog_157270 == true) {
				try{
					flixContent.value = await $fetch(`https://media.flixcar.com/delivery/webcall/match/10251/sl/ean/${item?.value?.ean_code}?resp=0`);
				} catch (e) {
					flixContent.value = null;
				}
			}

			let value = '';

			if (flixContent?.value?.event == 'matchhit') {
				value = '<div id="flix-inpage"></div>';
			} else if(item?.value?.check_lists?.webcatalog_157271 == true) {
				value = '<div class="loadbeeTabContent"><div class="loadbeeTab" data-loadbee-manufacturer="EAN" data-loadbee-product="' + item.value.ean_code + '" data-loadbee-language="sl_SI" data-loadbee-css="default" data-loadbee-button="default" data-loadbee-template="default"></div></div>';
			} else if(item?.value?.check_lists?.webcatalog_201949 == true) {
				value = '<iframe width="100%" src="https://bigbang.parhelion.hr/?ean=' + item.value.ean_code + '" frameborder="0" scrolling="auto" id="parhelion-frames"></iframe>';
			}

			if(flixContent?.value?.event == 'matchhit') {
				let script = document.createElement('script');

				script.setAttribute('type', 'text/javascript');
				script.setAttribute('src', '//media.flixfacts.com/js/loader.js');
				script.setAttribute('data-flix-distributor', '10251');
				script.setAttribute('data-flix-language', 'sl');
				script.setAttribute('data-flix-brand', item.value.manufacturer_title);
				script.setAttribute('data-flix-ean', item.value.ean_code);
				script.setAttribute('data-flix-sku', '');
				script.setAttribute('data-flix-button', 'flix-minisite');
				script.setAttribute('data-flix-inpage', 'flix-inpage');
				script.setAttribute('data-flix-button-image', '');
				script.setAttribute('data-flix-fallback-language', 'en');
				script.setAttribute('data-flix-autoload', 'inpage');
				script.setAttribute('data-flix-price', '');
				script.setAttribute('async', '');

				document.head.appendChild(script);
			}
			if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_157271 == true) {
				let script = document.createElement('script');
				script.setAttribute('src', 'https://button.loadbee.com/js/v2/loadbee.js');
				document.head.appendChild(script);
			}
			if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_201949 == true) {
				window.addEventListener('message', function (e) {
					if (e.hasOwnProperty('originalEvent')) {
						var origin = e.originalEvent.origin || e.origin;
					} else {
						var origin = e.origin;
					}
					if (origin !== 'https://bigbang.parhelion.hr') return
					let box = document.getElementById('parhelion-frames');
					if(box){
						box.style.height = e.data.frameHeight + 'px';
					}
				}, false);
			}
		}
	);

	const manufacturerContent = computed(() => {
		let value = '';

		if (flixContent?.value?.event == 'matchhit') {
			value = '<div id="flix-inpage"></div>';
		} else if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_157271 == true) {
			value = '<div class="loadbeeTabContent"><div class="loadbeeTab" data-loadbee-manufacturer="EAN" data-loadbee-product="' + item?.value?.ean_code + '" data-loadbee-language="sl_SI" data-loadbee-css="default" data-loadbee-button="default" data-loadbee-template="default"></div></div>';
		} else if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_201949 == true) {
			value = '<iframe width="100%" src="https://bigbang.parhelion.hr/?ean=' + item?.value?.ean_code + '" frameborder="0" scrolling="auto" id="parhelion-frames"></iframe>';
		}

		return value;
	});

	//webcatalog content
	onMounted(() => {
		if(flixContent?.value?.event == 'matchhit') {
			let script = document.createElement('script');

			script.setAttribute('type', 'text/javascript');
			script.setAttribute('src', '//media.flixfacts.com/js/loader.js');
			script.setAttribute('data-flix-distributor', '10251');
			script.setAttribute('data-flix-language', 'sl');
			script.setAttribute('data-flix-brand', item.value.manufacturer_title);
			script.setAttribute('data-flix-ean', item.value.ean_code);
			script.setAttribute('data-flix-sku', '');
			script.setAttribute('data-flix-button', 'flix-minisite');
			script.setAttribute('data-flix-inpage', 'flix-inpage');
			script.setAttribute('data-flix-button-image', '');
			script.setAttribute('data-flix-fallback-language', 'en');
			script.setAttribute('data-flix-autoload', 'inpage');
			script.setAttribute('data-flix-price', '');
			script.setAttribute('async', '');

			document.head.appendChild(script);
		}
		if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_157271 == true) {
			let script = document.createElement('script');
			script.setAttribute('src', 'https://button.loadbee.com/js/v2/loadbee.js');
			document.head.appendChild(script);
		}
		if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_201949 == true) {
			window.addEventListener('message', function (e) {
				if (e.hasOwnProperty('originalEvent')) {
					var origin = e.originalEvent.origin || e.origin;
				} else {
					var origin = e.origin;
				}
				if (origin !== 'https://bigbang.parhelion.hr') return
				let box = document.getElementById('parhelion-frames');
				if(box){
					box.style.height = e.data.frameHeight + 'px';
				}
			}, false);
		}
	});

	onUnmounted(() => {
		if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_157270 == true) {
			const script = document.querySelector("script[src='//media.flixfacts.com/js/loader.js']")
			if (script) {
				script.parentNode.removeChild(script);
			}
		}
		if(item?.value?.check_lists && item?.value?.check_lists.webcatalog_157271 == true) {
			const script = document.querySelector("script[src='https://button.loadbee.com/js/v2/loadbee.js']")
			if (script) {
				script.parentNode.removeChild(script);
			}
		}
		if(item?.value?.check_lists && item?.value?.check_lists?.webcatalog_201949 == true) {
			window.removeEventListener('message', function (e) {
				if (e.hasOwnProperty('originalEvent')) {
				var origin = e.originalEvent.origin || e.origin;
				} else {
				var origin = e.origin;
				}
				if (origin !== 'https://bigbang.parhelion.hr') return
				document.getElementById('parhelion-frames').style.height = e.data.frameHeight + 'px';
			});
		}
	});

	//comments
	let toggleCommentEl = ref(false);
	function toggleCommentContainer() {
		toggleCommentEl.value = !toggleCommentEl.value;
	}

	// rates chart
	const ratesChart = reactive([{ rate: 1, counter: 0 },{ rate: 2, counter: 0 },{ rate: 3, counter: 0 },{ rate: 4, counter: 0 },{ rate: 5, counter: 0 },]);
	const ratingsData = item?.value?.feedback_comment_widget.items;
	Object.values(ratingsData).forEach((rating) => {
		const rateIndex = Number(rating.rate) - 1;
		ratesChart[rateIndex].counter++;
	});
	const totalVotes = computed(() => Object.keys(ratingsData).length);
	const calculateWidth = (count) => {
		return totalVotes.value ? `${((count / totalVotes.value) * 100).toFixed(0)}%` : '0%';
	};
	
	//scroll to comments
	const comments = ref();
	function scrollToComments() {
		toggleCommentEl.value = true;

		if(comments.value) {
			const element = comments.value;
			const offset = -80;

			const elementPosition = element.getBoundingClientRect().top + window.scrollY;
			const offsetPosition = elementPosition + offset;

			window.scrollTo({top: offsetPosition,behavior: 'smooth'});
		}
	}

	// show/hide comments form
	const commentsForm = ref(0);

	//dynamic price
	let dynamicPriceDate = ref(null);
	let dynamicPriceActive = ref(false);
	let dynamicPriceCartPriceActive = ref(false);
	async function dynamicPriceSelect(item) {
		if(item?.extra_price_mode_dynamicprice === 'show_price') {
			var itemPriceIntg = parseFloat(item?.extra_price_dynamicprice);
			const options = {
				prices_cart: {
					code: item?.shopping_cart_code,
					category: 'dynamicprice',
					price: itemPriceIntg
				}
			}
			await useApi(`${endpoints.get('_post_hapi_customer')}`, {
				method: 'POST',
				body: options,
			},{fetchMode: 'ofetch'}).then(async (res) => {
				dynamicPriceCartPriceActive.value = true;
				await webshop.fetchCart();
			})

			dynamicPriceActive.value = false;
		} else {
			dynamicPriceActive.value = true;
		}
	}

	async function dynamicPriceRemove(item) {
		const options = {
			remove_prices_cart: item.shopping_cart_code,
		}
		await useApi(`${endpoints.get('_post_hapi_customer')}`, {
			method: 'POST',
			body: options,
		},{fetchMode: 'ofetch'}).then(async (res) => {
			dynamicPriceCartPriceActive.value = false;
			await webshop.fetchCart();
		})

		dynamicPriceActive.value = false;
	}

	watch(
		() => dynamicPriceCartPriceActive.value,
		async (newValue) => {
			const url = getLastUrlSegment(route.path).split('-');
			const productId = url[url.length - 1];
			const slug = url.slice(0, -2).join('-');
			const fetchOptions = {
				item_id: productId,
				item_slug: slug,
			};

			const urlParams = Object.keys(fetchOptions).length ? '?' + new URLSearchParams(fetchOptions).toString() : '';
			await useApi(`${endpoints.get('_get_hapi_catalog_product')}${urlParams}`, {
				method: 'GET',
			},{fetchMode: 'ofetch'})
			.then(async (res) => {
				item.value = res.data;
				dynamicPriceDate.value = res.data?.price_custom_prices_cart_expire;

				if (dynamicPriceDate.value) {
					setInterval(updateDynamicPriceTimer, 1000);
				}

				// generate thumbnails
				await imageThumbs();
			})
		}
	)

	//dynamic price countdown
	const remainingTime = ref(0);
	const countdownFormatted = computed(() => {
		const targetTime = new Date(dynamicPriceDate.value * 1000);
		const currentTime = new Date();
		remainingTime.value = Math.max(0, Math.floor((targetTime - currentTime) / 1000));

		const hours = Math.floor(remainingTime.value / 3600);
		const minutes = Math.floor((remainingTime.value % 3600) / 60);
		const seconds = remainingTime.value % 60;

		return `
			<span><span class="value semi-bold hours">${hours}</span><span class="frame"> ur </span></span>
			<span><span class="value semi-bold">${minutes}</span><span class="frame"> min </span></span>
			<span><span class="value semi-bold">${seconds}</span><span class="frame"> s</span></span>
		`;
	});

	const updateDynamicPriceTimer = () => {
		remainingTime.value = Math.max(0, remainingTime.value - 1);
	};

	//services
	const availableInsurances = ref([]);
	const availableServices = ref([]);
	const selectedServices = ref([]);
	const serviceBid = ref(null);
	const serviceActive = ref(false);
	const insuranceActive = ref(false);

	function watchServices() {
		availableInsurances.value = [];
		if (item?.value?.insurance_simple?.available) {
			item?.value?.insurance_simple?.available.forEach(el => availableInsurances.value.push(el));
		}

		availableServices.value = [];
		if (item?.value?.service_simple?.available) {
			item?.value?.service_simple?.available.forEach(el => {
				if (!el.options?.selected_value) {
					availableServices.value.push(el);
				}
			});
		}
	}

	function serviceUpdate() {
		let services = []
		let insurance = []
		if(selectedServices.value && item?.value?.service_simple?.available.some(item => selectedServices.value.includes(item.id))) {
			services = item?.value?.service_simple?.available.filter(item => selectedServices.value.includes(item.id));
			serviceActive.value = true;
		}
		else {
			services = [item?.value?.service_simple?.available[0]];
			serviceActive.value = false;
		}

		if(selectedServices.value.length && item?.value?.insurance_simple?.available.some(item => selectedServices.value.includes(item.id))) {
			insurance = item?.value?.insurance_simple.available.filter(item => selectedServices.value.includes(item.id));
			insuranceActive.value = true;
		}
		else {
			insurance = [item?.value?.insurance_simple?.available[0]];
			insuranceActive.value = false;
		}

		serviceBid.value = {
			service: services,
			insurance: insurance
		};
	}

	watch(
		() => item?.value,
		() => {
			watchServices();
			serviceUpdate();
		}
	);

	//shipping info
	//FIXME INTEG jezik
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	const calendarMonth = ['januarja', 'februarja', 'marca', 'aprila', 'maja', 'junija', 'julija', 'avgusta', 'septembra', 'oktobra', 'novembra', 'decembra'];
	const calendarDays = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote'];

	const shippingDate = computed(() => {
		return item?.value?.shipping_options?.map((item) => {
			const shippingDateDay = new Date(item.min_delivery_date * 1000).getDay();
			const shippingDateMonth = new Date(item.min_delivery_date * 1000).getMonth();

			if (calendarDays[shippingDateDay] && calendarMonth[shippingDateMonth]) {
				if (new Date(item.min_delivery_date * 1000).toDateString() === new Date().toDateString() || new Date(item.min_delivery_date * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
					return new Date(item.min_delivery_date * 1000).getDate() + '.' + (new Date(item.min_delivery_date * 1000).getMonth() + 1) + '.';
				} else {
					return calendarDays[shippingDateDay] + ', ' + new Date(item.min_delivery_date * 1000).getDate() + '.' + (new Date(item.min_delivery_date * 1000).getMonth() + 1) + '.';
				}
			} else {
				return new Date(item.min_delivery_date * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
			}
		});
	});

	const date = (item.value.status == 5) ? item.value.shipping_date : item.value.shipping_options?.find(option => option.id === 'p')?.min_delivery_date || null;
	const pickupDate = computed(() => {
		const pickupDateDay = new Date(date * 1000).getDay();
		const pickupDateMonth = new Date(date * 1000).getMonth();

		if (calendarDays[pickupDateDay] && calendarMonth[pickupDateMonth]) {
			if (new Date(date * 1000).toDateString() === new Date().toDateString() || new Date(date * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
				return new Date(date * 1000).getDate() + '.' + (new Date(date * 1000).getMonth() + 1) + '.';
			} else {
				return calendarDays[pickupDateDay] + ', ' + new Date(date * 1000).getDate() + '.' + (new Date(date * 1000).getMonth() + 1) + '.';
			}
		} else {
			return new Date(date * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
		}
	});

	//sellers
	const firstOtherOffer = computed(() => {
		return item?.value?.first_other_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_other_offer_id.toString()) : null;
	});
	const firstMainOffer = computed(() => {
		return item?.value?.first_main_seller_offer_id ? item?.value?.offers?.find(offer => offer.id === item?.value?.first_main_seller_offer_id.toString()) : null;
	});
	const offersFlyoutTitle = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_flyout_title')
			.replace('%s%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text);
	});
	const offersDescl = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_others')
			.replace('%offers_total%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text)
			.replace('%price%', firstOtherOffer.value.price_custom ?? 0);
	});

	//energy
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35'];
		return item?.value?.attributes_special?.find(attr => targetAttributeCodes.includes(attr.attribute_code)) || null;
	});

	//flyout open
	function flyoutOpen(mode, value) {
		//energy
		if(mode == 'energy') {
			emit('flyoutUpdate', {'flyout_mode': 'energy', 'flyout_title': labels.get('energy_flyout_title'), 'flyout_content': value ? value : null});
		}

		//shipping
		if(mode == 'shipping') {
			const content = {
				shipping_dates: shippingDate,
				date: item.value.shipping_date,
				status: item.value.status,
				shipping_options: item.value.shipping_options
			};

			emit('flyoutUpdate', {'flyout_mode': 'shipping', 'flyout_title': labels.get('shipping'), 'flyout_content': content ? content : null});
		}

		//stores
		//FIXME INTEG dokumentaicija
		if(mode == 'stores') {
			let title = labels.get('item_stores_flyout_title');
			if(item?.value?.status == 4 && item?.value?.warehouses_display) {
				title = labels.get('item_stores_flyout_title')
			}

			const content = {
				status: item.value.status,
				warehouses_single_pickup_display: item.value.warehouses_single_pickup_display,
				last_piece_sale: item.value.last_piece_sale,
				warehouses_display: item.value.warehouses_display,
				is_available: item.value.is_available,
				shipping_options: item.value.shipping_options
			};

			emit('flyoutUpdate', {'flyout_mode': 'stores', 'flyout_title': title, 'flyout_content': content ? content : null});
		}

		//seller
		if(mode == 'seller') {
			emit('flyoutUpdate', {'flyout_mode': 'sellers', 'flyout_title': offersFlyoutTitle, 'flyout_content': value && value.offers ? value.offers : null, 'flyout_offer_id': value && value.id ? value.id : null});
		}
	}
</script>

<style lang="less" scoped>
	//tabs
	.cd-tab{
		display: block; margin-top: 24px; padding: 0 16px; background: var(--white); border-radius: 12px;

		@media (max-width: @m){margin: 0 0 12px; padding: 0 12px; border-radius: 0;}
	}
	.cd-tab-title{
		display: flex; align-items: center; height: 67px; font-size: 24px; line-height: 1.4; font-weight: 600; color: var(--black); position: relative; cursor: pointer;
		&:before{.icon-arrow-down(); font: 12px/1 var(--fonti); font-weight: normal; color: var(--black); position: absolute; right: 0; .transition(transform);}
		&.active:before{.rotate(180deg);}
		&.no-icon{
			cursor: default;
			&:before{content: none;}
		}

		@media (max-width: @m){
			height: 55px; font-size: 18px;
			&:before{font-size: 10px;}
			&.active:before{.rotate(180deg);}
		}
	}
	.cd-tab-content{padding-bottom: 18px;}
	.cd-tab-more{
		display: inline-flex; font-size: 12px; text-decoration: underline; cursor: pointer;
		@media (min-width: @t){
			&:hover{text-decoration: none;}
		}
	}

	//main
	.cd-bc{
		padding-bottom: 32px;
		
		@media (max-width: @m){display: none;}
	}
	.cd-main{
		display: flex; margin-bottom: 58px; position: relative;

		@media (max-width: @m){flex-direction: column-reverse; margin: 0 0 12px;}
	}
	.cd-container{
		margin-bottom: 24px; padding: 18px 16px; background: var(--white); border-radius: 12px; position: relative;
		&.flyout{
			padding-right: 50px; cursor: pointer;
			&:before{.icon-arrow-down(); font: 12px/1 var(--fonti); color: var(--black); position: absolute; right: 18px; top: 50%; transform: translateY(-50%) rotate(-90deg);}
		}

		@media (max-width: @m){
			margin-bottom: 12px; padding: 18px 12px; border-radius: 0;
			&.flyout:before{right: 12px;}
		}
	}
	.cd-container-title{
		display: flex; align-items: center; font-size: 18px; font-weight: 600; position: relative;
		&.icon{
			padding-left: 30px;
			&:before{font: 15px/1 var(--fonti); color: var(--black); position: absolute; left: 0;}
		}
		&.shipping:before{.icon-shipping(); font-size: 17px;}
		&.stores:before{.icon-pin(); font-size: 19px; left: 3px;}
		&.seller:before{.icon-store(); font-size: 19px; left: 2px;}
		&.energy:before{.icon-energy(); font-size: 19px; left: 2px;}

		@media (max-width: @m){
			font-size: 16px;
			&.icon{
				padding-left: 28px;
				&:before{font-size: 14px;}
			}
			&.shipping:before{font-size: 15px;}
			&.stores:before{font-size: 17px; left: 2px;}
			&.seller:before{font-size: 17px; left: 1px;}
			&.energy:before{font-size: 17px; left: 1px;}
		}
	}
	.cd-container-content{
		margin-top: 16px; font-size: 15px;

		@media (max-width: @m){margin-top: 12px; font-size: 12px; line-height: 1.3;}
	}
	.cd-col1{
		width: 952px; flex-shrink: 0; margin-right: 24px;

		@media (max-width: @l){width: 700px;}
		@media (max-width: @t){width: 550px;}
		@media (max-width: @m){width: 100%; margin-right: 0;}
	}

	//attribute
	.cd-tab-attributes{
		display: grid; grid-template-columns: 1fr 1fr; gap: var(--elementGap); font-size: 14px; line-height: 1.2; position: relative;
		&:before{.pseudo(1px,auto); background: var(--gray2); position: absolute; left: 50%; top: 0; bottom: 12px; z-index: 1;}

		@media (max-width: @m){
			display: block; gap: 14px; font-size: 12px;
			&:before{content: none;}
		}
	}
	.cd-tab-attribute{
		display: flex;

		@media (max-width: @m){
			margin-bottom: 14px;
			&:last-child{margin-bottom: 0;}
		}
	}
	.cd-tab-attribute-title{width: 50%; flex-shrink: 0; padding-right: 30px; color: #6D6D6D;}
	.cd-tab-attribute-value{width: 50%;}
	.cd-tab-more-attr{margin-bottom: 18px;}

	//description
	.cd-tab-content-desc{
		font-size: 14px; line-height: 1.5;
		:deep(ul), :deep(ol){margin: 0 0 24px 20px;}
		:deep(h2), :deep(h3), :deep(h4), :deep(h5){padding: 15px 0 10px; font-weight: 600;}
		:deep(iframe){width: 100%;}
		:deep(img){width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
	.cd-tab-more-desc{margin-top: 24px;}

	//comments/rates
	.cd-comments-info{display: flex; align-items: center;}
	.cd-comments-info-col1{flex-grow: 1; padding-right: 30px;}
	.cd-comments-rate-average{margin-bottom: 12px; font-size: 28px; font-weight: 600; line-height: 1.2; letter-spacing: -0.28px;}
	.cd-chart-items{display: flex; flex-direction: column-reverse; width: 372px; flex-shrink: 0;}
	.cd-chart-item{display: flex; align-items: center; width: 100%; gap: 6px; font-size: 11px; color: #6D6D6D;}
	.cd-chart-rate{display: flex; align-items: center; flex-shrink: 0; width: 60px; margin-right: 6px; position: relative;}
	.cd-chart-bar{display: flex; align-items: center; flex-grow: 1; height: 4px; background: var(--gray2); border-radius: 22px; position: relative;}
	.cd-chart-progress-bar{height: 4px; background: var(--blueDark); border-radius: 22px; position: absolute; top: 0; bottom: 0; left: 0; z-index: 1;}
	.cd-chart-qty{width: 10px; margin-left: 6px; text-align: center;}
	.cd-comments-form-button{margin-top: 12px; padding: 0 24px;}
	.cd-comments{margin-top: 28px;}
	.cd-comment-items{column-count: 2; column-gap: 12px;}
	.cd-btn-container{display: flex; justify-content: center; margin-top: 16px;}

	//main info
	.cd-col2{flex-grow: 1;}
	.cd-brand{
		display: flex; margin-bottom: 6px; font-size: 14px;
		a{text-decoration: underline; color: var(--black);}
		@media (min-width: @t){
			a:hover{text-decoration: none;}
		}
	}
	.cd-title{
		display: block; padding: 0; margin-bottom: 12px; font-size: 20px; line-height: 1.3; color: var(--black);
		
		@media (max-width: @m){margin-bottom: 6px; font-size: 16px;}
	}

	.cd-info{display: flex; align-items: center;}
	.cd-rate-section{display: flex; align-items: flex-end; flex-grow: 1; padding-right: 20px; font-size: 11px; font-weight: 300; color: var(--black);}
	.cd-rate{
		display: flex; align-items: center;
		:deep(.icon-star-empty){
			display: inline-block; position: relative; width: 18px; height: 18px; margin-right: 2px; opacity: 1!important;
			&:after{.pseudo(18px,18px); background: url(assets/images/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
			
		}
		:deep(.icon-star){
			width: 19px; height: 19px;
			&:after{width: 19px; height: 19px; background: url(assets/images/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
		}

		@media (max-width: @m){
			:deep(.icon-star-empty){
				width: 15px; height: 15px; margin-right: 1px;
				&:after{width: 15px; height: 15px;}
				
			}
			:deep(.icon-star){
				width: 16px; height: 16px;
				&:after{width: 16px; height: 16px;}
			}
		}
	}
	.cd-rate-average{
		margin-left: 3px;

		@media (max-width: @m){margin: 0 4px 0 0; font-size: 12px; line-height: 1.2; font-weight: 600;}
	}
	.cd-rate-votes{
		margin-left: 5px; text-decoration: underline; cursor: pointer;
		&:hover{text-decoration: none;}

		@media (max-width: @m){margin-left: 4px;}
	}
	.cd-code{display: block; font-size: 12px; color: var(--gray5);}

	//price
	.cd-price{
		display: flex; align-items: center; margin-top: 24px;
		.red{color: var(--errorColor);}

		@media (max-width: @m){align-items: baseline; margin-top: 18px;}
	}
	.cd-current-price{
		font-size: 28px; font-weight: 700;
		:deep(.p-comma){display: none;}
		:deep(.p-d){font-size: 16px; vertical-align: text-top;}
	}
	.cd-old-price{
		margin-left: 12px; font-size: 14px; color: var(--black);

		@media (max-width: @m){margin-left: ;}
	}
	.cd-price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 15px; height: 15px; margin-left: 8px; position: relative;
		&:before{.icon-info(); font: 15px/1 var(--fonti); color: var(--black);}
		&:hover .cd-price-tooltip{display: flex;}
	}
	.cd-price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: var(--black); position: absolute; right: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); right: 12px; z-index: 1; .rotate(45deg);}
	}
	.cd-price-installment{
		display: block; margin-top: 8px; font-size: 14px;
		:deep(p){padding: 0;}

		@media (max-width: @m){font-size: 12px;}
	}

	//dynamic price
	.cd-conf-price-label{
		display: none; margin-top: 8px; font-size: 14px; color: var(--black);
		&.active{display: block;}
		:deep(a){
			color: var(--black);
			&:hover{text-decoration: none;}
		}
	}
	.cd-conf-price-section{width: 100%; margin-top: 12px;}
	.cd-conf-price-header{display: flex; align-items: center; margin-bottom: 6px;}
	.cd-conf-price-title{flex-grow: 1; font-size: 14px; color: var(--errorColor);}
	.cd-conf-price-remove{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 20px; height: 20px; background: var(--gray3); border-radius: 100%; font-size: 0; line-height: 0; text-decoration: none; position: relative; .transition(background); cursor: pointer;
		&:before{.icon-x(); font: 10px/1 var(--fonti); color: var(--black); font-weight: 600; text-indent: 1px;}
	}
	.cd-conf-price{
		display: flex; align-items: center; flex-grow: 1;
		span{display: block;}
		.cd-current-price{flex-grow: 1; flex-shrink: 0; color: var(--errorColor);}
	}
	.cd-conf-price-timer{display: flex; align-items: center; flex-shrink: 0; font-size: 15px; color: var(--gray5); position: relative;}
	.cd-conf-price-timer-bottom{
		display: flex; align-items: center;
		.countdown{
			display: flex; flex-shrink: 0;
			:deep(&>span){display: flex; justify-content: center; min-width: 36px; margin-left: 12px; position: relative;}
		}
		:deep(.value){font-size: 28px; font-weight: 600; color: var(--errorColor); position: relative;}
		:deep(.frame){font-size: 8px; font-weight: 400; text-align: center; color: var(--gray5); position: absolute; bottom: 0; top: 100%;}
	}
	.cd-conf-price-note{display: block; width: 100%; margin-top: 22px; font-size: 10px; color: var(--gray5); position: relative;}

	//services
	.cd-services{margin-top: 24px;}
	.cd-service{
		flex-grow: 1; margin-top: 16px; padding: 16px 50px 16px 12px; background: var(--gray3); border-radius: 8px; font-size: 15px; cursor: pointer; position: relative;
		&:before{.icon-arrow-down(); font: 12px/1 var(--fonti); color: var(--black); position: absolute; right: 18px; top: 50%; transform: translateY(-50%) rotate(-90deg);}

		@media (max-width: @m){
			font-size: 12px;
			&:before{right: 8px;}
		}
	}
	.cd-service-title{
		display: flex; padding-left: 32px; font-size: 18px; font-weight: 600; position: relative;
		&:before{.icon-service(); font: 22px/1 var(--fonti); color: var(--blueDark); position: absolute; top: 1px; left: 0;}
		&.insurance:before{.icon-insurance();}

		@media (max-width: @m){
			padding-left: 26px; font-size: 14px;
			&:before{font-size: 18px;}
		}
	}
	.cd-service-bid{display: block; margin-top: 6px;}

	//available
	.cd-available-qty{
		display: flex; align-items: center; margin-top: 24px; padding-left: 24px; font-size: 18px; font-weight: 600; color: #1FB549; position: relative;
		&:before{.icon-check-round(); font: 15px/1 var(--fonti); color: #1FB549; position: absolute; left: 0;}

		@media (max-width: @m){
			padding-left: 20px; font-size: 16px;
			&:before{font-size: 14px;}
		}
	}

	//add to cart
	.cd-add-to-cart{display: flex; justify-content: center; margin-top: 24px;}
	.cd-btn-add{
		width: 100%; height: 60px; border-radius: 30px; font-size: 18px; cursor: pointer;
		span{
			padding-left: 32px; position: relative;
			&:before{.icon-cart(); font: 21px/1 var(--fonti); color: var(--white); position: absolute; left: 0;}
		}

		@media (max-width: @m){height: 52px; border-radius: 26px; font-size: 15px;}
	}

	//shipping
	.cd-shipping-desc{
		display: block; margin-bottom: 16px; font-size: 15px; line-height: 1.35;
		&:last-child{margin-bottom: 0;}

		@media (max-width: @m){margin-bottom: 3px; font-size: 12px; line-height: 1.3;}
	}

	//energy
	.cd-energy-content{
		display: flex; align-items: center;
		:deep(img){display: block; margin-right: 12px; width: auto; height: auto; max-width: 40px; max-height: 22px;}
	}

	.age-verification{
		.cd-container, .cd-tab{
			*, &:before, &:after{filter: blur(4px);}
		}
	}
</style>