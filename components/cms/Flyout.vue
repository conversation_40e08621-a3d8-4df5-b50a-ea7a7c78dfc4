<template>
	<BaseUiModal v-slot="{onClose, item}" name="flyout" :key-closable="true">
		<div class="flyout">
			<div class="flyout-body">
				<div class="close" @click="onClose()">X</div>
				<FlyoutInstallments v-if="item.mode == 'installments'" />
				<LazyFlyoutShipping v-else-if="item.mode == 'shipping'" />
				<LazyFlyoutServices v-else-if="['insurance', 'service'].includes(item.mode)" />
				<LazyFlyoutStores v-else-if="item.mode == 'stores'" />
				<template v-else>
					<FlyoutLayout>
						<div v-if="item?.content" v-html="item?.content" />
					</FlyoutLayout>
				</template>
			</div>
			<div class="flyout-mask" @click="onClose()" />
		</div>
		<!--
		<Body :class="{'flyout-page cd-flyout-active': data && bus.event == 'flyoutUpdate'}" />
		<div class="cd-flyout" :class="{'active': data && bus.event == 'flyoutUpdate'}" ref="flyoutContainer">
			<div class="cd-flyout-close" @click="flyoutClose()"></div>
			<div class="cd-flyout-content">
				<div v-if="flyoutTitle" class="cd-flyout-header">
					<div class="cd-flyout-header-title" v-html="flyoutTitle"></div>
				</div>
				<div v-if="flyoutMode == 'safety'">
					<CatalogWidgetDetailFlyoutSafety :data="flyoutContent" />
				</div>
				<div v-if="(flyoutMode && flyoutMode == 'insurance') || (flyoutMode && flyoutMode == 'service')">
					<div class="cd-flyout-service-content">
						<div v-for="service in flyoutMode == 'insurance' ? flyoutContent.insurances : flyoutContent.services" :key="service.id">
							<input v-if="service.category_type == 's'" class="special" v-model="currentlySelectedInsurance" type="radio" name="service" :value="service.id" :id="'service-' + service.id" />
							<input v-else class="special" v-model="currentlySelected" type="checkbox" name="service" :value="service.id" :id="'service-' + service.id" />
							<label class="cd-flyout-service-label" :for="'service-' + service.id">
								<div class="cd-flyout-service-title">
									<span class="title">{{ service.title }}</span>
									<span class="cd-flyout-service-price">{{ formatCurrency(service.price) }}</span>
								</div>
								<div class="cd-flyout-service-desc" v-if="service.description" v-html="service.description"></div>
							</label>
						</div>
						<div class="cd-flyout-service" v-if="flyoutMode == 'insurance'">
							<div class="cd-flyout-service-top">
								<input :checked="currentlySelectedInsurance == null" v-model="currentlySelectedInsurance" type="radio" value="" name="service" id="service-0" />
								<label class="cd-flyout-service-label" for="service-0">
									<div class="cd-flyout-service-title">{{ labels.get('no_warranty') }}</div>
								</label>
							</div>
						</div>
					</div>
				</div>
				<div v-if="flyoutMode == 'energy' && flyoutContent" class="cd-energy-container-img">
					<BaseUiImage :data="flyoutContent" default="/images/no-image-50.jpg" />
				</div>
				<template v-else-if="flyoutMode == 'loyaltyLoggedIn'">
					<BaseCmsLabel code="loyalty_flyout_content_user" tag="div" class="cd-loyalty-flyout-list" />
					<BaseCmsLabel code="loyalty_flyout_btn_user" tag="div" class="cd-loyalty-flyout-btn" />
					<BaseCmsLabel code="loyalty_flyout_box" tag="div" class="cd-loyalty-flyout-box" />
				</template>
				<template v-else-if="flyoutMode == 'loyaltyNotLoggedIn'">
					<BaseCmsLabel code="loyalty_flyout_content" tag="div" class="cd-loyalty-flyout-list" />
					<BaseCmsLabel code="loyalty_flyout_btn" tag="div" class="cd-loyalty-flyout-btn" />
					<BaseCmsLabel code="loyalty_flyout_box" tag="div" class="cd-loyalty-flyout-box" />
				</template>
				<template v-else-if="flyoutMode == 'mBadges' && flyoutContent">
					<div v-if="flyoutContent.selected_price && flyoutContent.selected_price == 'uau' && labels.get('uau_badge_special_title')" class="cd-badge-item-m">
						<div v-if="labels.get('uau_badge_special') != 'uau_badge_special'" class="cd-badge-item-img-m">{{ labels.get('uau_badge_special') }}</div>
						<div class="cd-badge-item-cnt-m">
							<BaseCmsLabel code="uau_badge_special_title" tag="div" class="cd-badge-item-title-m" />
							<BaseCmsLabel code="uau_badge_special_short_description" tag="span" />
							<BaseCmsLabel v-if="labels.get('uau_badge_special_content')" code="uau_badge_special_content" tag="span" />
						</div>
					</div>

					<template v-for="(flyoutContent, index) in flyoutContent?.badges_special_2" :key="index">
						<template v-if="flyoutContent.title || flyoutContent.short_description  || flyoutContent.content">
							<div class="cd-badge-item-m">
								<div v-if="flyoutContent.gift_image" class="cd-badge-item-img-m">
									<BaseUiImage loading="lazy" :src="flyoutContent.gift_image_upload_path" width="44" height="44" default="/images/no-image-50.webp" :alt="flyoutContent.title" />
								</div>
								<div v-else-if="flyoutContent.badge_image" class="cd-badge-item-img-m">
									<BaseUiImage loading="lazy" :src="flyoutContent.badge_image_upload_path" width="44" height="44" default="/images/no-image-50.webp" :alt="flyoutContent.title" />
								</div>
								<div class="cd-badge-item-cnt-m">
									<div v-if="flyoutContent.title" class="cd-badge-item-title-m" v-html="flyoutContent.title"></div>
									<span v-if="flyoutContent.short_description" v-html="flyoutContent.short_description"></span>
									<span v-if="flyoutContent.content" v-html="flyoutContent.content"></span>
								</div>
							</div>
						</template>
					</template>
				</template>
				<template v-else-if="flyoutMode == 'sellers' && flyoutContent">
					<div v-for="(item, index) in flyoutContent" :key="item.id" class="cd-seller-item" :class="{'active': item.id == offerId}">
						<div class="cd-seller-item-col1">
							<div class="cd-seller-item-info">
								<div class="cd-seller-item-price">{{ formatCurrency(item.price_custom) }}</div>
								<div class="badge" v-if="sellerBadge(item.badges)">{{sellerBadge(item.badges)}}</div>
								<div v-if="item.product_condition && item.product_condition != 'n'" class="cp-condition-badge cd-condition-badge">
									<BaseCmsLabel :code='"condition_" + item.product_condition' tag="span" />
								</div>
							</div>
							<div v-if="item.status && item.status == 7" class="cd-seller-item-status"><BaseCmsLabel code="ni_na_zalogi" /></div>
							<div v-else-if="shippingDate" class="cd-seller-item-shipping">
								<span v-if="new Date(offerShippingDate[index] * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('seller_item_time_of_delivery_today').replace('%s%', shippingDate[index])"></span>
								<span v-else-if="new Date(offerShippingDate[index] * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('seller_item_time_of_delivery_tomorow').replace('%s%', shippingDate[index])"></span>
								<span v-else v-html="labels.get('seller_item_time_of_delivery').replace('%s%', shippingDate[index])"></span>
								<template v-if="item.shipping_options?.length">
									<template v-if="item.shipping_options[0].shipping_price <= 0">
										- <strong class="green"><BaseCmsLabel code="free" /></strong>
									</template>
								</template>
							</div>
							<div class="cd-seller-item-bottom">
								<div class="cd-seller-item-title">
									<NuxtLink v-if="item.seller_title" :to="item.seller_url_product_page ? item.seller_url_product_page_without_domain : item.seller_url_without_domain">{{ item.seller_title }}</NuxtLink>
								</div>
							</div>
						</div>
						<div class="cd-seller-item-btn btn" @click="selectOffer(item);">
							<span class="selected">{{ labels.get('seller_selected') }}</span>
							<span class="unselected">{{ labels.get('seller_select') }}</span>
						</div>
					</div>
				</template>
				<div v-else-if="flyoutMode == 'shipping_locations'" class="wc-stores">
					<BaseCmsLabel v-if="flyoutContent.shippingLocations.true" code="store_available" tag="div" class="wc-stores-subtitle green" />
					<article class="clear wc-store" v-for="location in flyoutContent.shippingLocations.true" :key="location.id">
						<input v-model="currentlySelectedLocation" type="radio" name="shipping_location" :id="'location-' + location.id" :value="location.id" />
						<label :for="'location-' + location.id">
							<div class="wc-store-title">{{ location.title }}</div>
						</label>
					</article>
					<div v-if="flyoutContent.shippingLocations.false" class="wc-stores-subtitle red" :class="{'special' : flyoutContent.shippingLocations.true}"><BaseCmsLabel code="store_empty_stock" /></div>
					<article class="clear wc-store" v-for="location in flyoutContent.shippingLocations.false" :key="location.id">
						<input v-model="currentlySelectedLocation" type="radio" name="shipping_location" :id="'location-' + location.id" :value="location.id" />
						<label :for="'location-' + location.id">
							<div class="wc-store-title">{{ location.title }}</div>
						</label>
					</article>
					<BaseCmsLabel code="item_stores_note_cart_shipping" tag="div" class="cd-store-note special" />
				</div>
				<div v-else-if="flyoutContent && flyoutMode != 'insurance' && flyoutMode != 'service' && flyoutMode != 'shipping_locations' && flyoutMode != 'safety'" v-html="flyoutContent"></div>
				<div class="cd-flyout-bottom" :class="{'special': flyoutMode == 'shipping_locations' || flyoutMode == 'sellers' || flyoutMode == 'service' || flyoutMode == 'insurance'}">
					<div v-if="formLoading" class="cd-flyout-close-btn btn btn-lightBlue btn-spinner"></div>
					<div v-else-if="flyoutMode == 'sellers' || flyoutMode == 'service' || flyoutMode == 'insurance'" class="cd-flyout-close-btn btn btn-lightBlue" @click="flyoutClose()">
						{{ labels.get('flyout_close_confirm') }}
					</div>
					<div v-else-if="flyoutMode == 'shipping_locations'" class="cd-flyout-close-btn btn btn-lightBlue" :class="{'btn-disabled': !currentlySelectedLocation.length}" @click="updateShipping()">
						{{ labels.get('flyout_close_confirm') }}
					</div>
					<div v-else class="cd-flyout-close-label" @click="flyoutClose()">{{ labels.get('flyout_close') }}</div>
				</div>
			</div>
		</div>
		-->
	</BaseUiModal>
</template>

<script setup>
	import {config} from '@/hapi.config';
	/*
	const webshop = useWebshop();
	const labels = useLabels();
	const {bus, emit} = useEventBus();
	const {formatDate} = useText();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['data']);
	let formLoading = ref(false);
	const { selectedShipping } = useCheckout();
	const router = useRouter();
	const {onClickOutside} = useDom();

	const flyoutTitle = computed(() => {
		return props.data && props.data.flyout_title ? props.data.flyout_title : null;
	});
	const flyoutContent = computed(() => {
		return props.data && props.data.flyout_content ? props.data.flyout_content : null;
	});
	const flyoutMode = computed(() => {
		return props.data && props.data.flyout_mode ? props.data.flyout_mode : null;
	});
	const offerId = computed(() => {
		return props.data && props.data.flyout_offer_id ? props.data.flyout_offer_id : null;
	});

	// seller UAU badges https://markerdoo.eu.teamwork.com/app/tasks/26431834
	function sellerBadge(badges) {
		if(!badges?.length) return null;

		let badgesToShow = ['1220795', '1220796', '1220797', '1220798', '1220799', '1220800', '1567938', '1567940', '1567943', '1567946', '1567947']; // prod
		if(['mp', 'local'].includes(config.default)) {
			badgesToShow = ['2232753', '2232754', '2232755', '2232756', '2232757', '2232758']; // dev
		}

		const badgeItems = badges.filter(b => badgesToShow.includes(b.id));
		return badgeItems?.length ? badgeItems[0].title : null;
	}

	// close modal on outside click and escape keyup
	const flyoutContainer = ref(null);
	const {init: initOnClickOutside, destroy: removeOnClickOutside} = onClickOutside(flyoutContainer, () => flyoutClose(), {manual: true});
	watch(
		() => bus.value,
		async (newValue) => {
			if(props.data && newValue.event == 'flyoutUpdate') {
				await new Promise(resolve => setTimeout(resolve, 300));
				initOnClickOutside();
			}
		}
	);

	//close
	async function flyoutClose() {
		if(bus.value.event == 'flyoutUpdate' && (flyoutMode.value == 'service' || flyoutMode.value == 'insurance')) {
			await updateService()
		}

		if(bus.value.event == 'flyoutUpdate') {
			bus.value.data = null;
		}

		currentlySelectedLocation.value = [];
		formLoading.value = false;
		removeOnClickOutside();
	}

	//close on route change
	watch(
		() => router.currentRoute.value.path,
		(newPath, oldPath) => {
			flyoutClose();
		}
	);

	//shipping location
	const currentlySelectedLocation = ref([]);

	watch(() => flyoutContent.value?.selectedLocation?.id, (newValue, oldValue) => {
		currentlySelectedLocation.value = newValue;
	});

	async function updateShipping() {
		formLoading.value = true;
		await webshop.updateShipping({
				shipping_id: flyoutContent.value.shippingId,
				shopping_cart_codes: flyoutContent.value.shoppingCartCodes,
				shipping_data: {
					location_point_id: currentlySelectedLocation.value,
				}
		}).then(() => {
			selectedShipping.value = flyoutContent.value?.shippingTitle ? flyoutContent.value.shippingTitle : null;
			flyoutClose();
		});
	}

	//services
	const currentlySelectedInsurance = ref(null);
	const currentlySelected = ref([]);

	watch(() => flyoutContent.value && flyoutContent.value.cartServiceSelect, (newValue, oldValue) => {
		if(flyoutContent.value?.cartServiceSelect) {
			if (flyoutContent.value.selectedInsurance) {
				currentlySelectedInsurance.value = flyoutContent.value.selectedInsurance;
			}

			if (flyoutContent.value.selectedServices) {
				let selectedServices = [];
				if (flyoutContent.value.selectedServices) {
					flyoutContent.value.selectedServices.forEach(el => {
						selectedServices.push(el.id);
					});
				}
				currentlySelected.value = selectedServices;
			}
		}
	});

	async function updateService() {
		formLoading.value = true;
		let data = [];
		let dataInfo = [];

		if (currentlySelectedInsurance.value != null && currentlySelectedInsurance.value !== "") data.push(currentlySelectedInsurance.value);
		if(currentlySelectedInsurance.value === "") {
			currentlySelectedInsurance.value = null
		}
		currentlySelected.value.forEach(el => data.push(el));

		dataInfo = flyoutContent.value.servicesAll?.flatMap(item => item?.items?.filter(subItem => data?.includes(subItem?.id)));

		if (flyoutContent.value.cartServiceSelect) {
			await webshop.updateProduct([{
				shopping_cart_code: flyoutContent.value.shopping_cart_code,
				quantity: flyoutContent.value.quantity,
				services: data.length ? data : [''],
			}])
		}

		formLoading.value = false;

		emit('servicesSelected', {'selectedItems': data ? data : null, 'selectedItemsInfo': dataInfo ? dataInfo : null});
	};

	watch(
		() => bus.value,
		(newValue) => {
			if(newValue.event === 'confItem') {
				currentlySelectedInsurance.value = null;
			}
		}
	);

	//seller shipping date
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	const offerShippingDate = computed(() => {
		return flyoutContent.value.map((item) => {
			let offerShipping = item?.shipping_options?.find(option => option.id === item?.shipping_type) || null;
			return item.status == '5' ? item?.date_available : offerShipping?.min_delivery_date;
		});
	});
	const shippingDate = computed(() => {
		const calendarMonth = ['januarja', 'februarja', 'marca', 'aprila', 'maja', 'junija', 'julija', 'avgusta', 'septembra', 'oktobra', 'novembra', 'decembra'];
		const calendarDays = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote'];

		return flyoutContent.value.map((item) => {
			let offerShipping = item?.shipping_options?.find(option => option.id === item?.shipping_type) || null;
			let offerShippingDate =  item.status == '5' ? item?.date_available : offerShipping?.min_delivery_date;
			if (offerShippingDate) {
				const shippingDateDay = new Date(offerShippingDate * 1000).getDay();
				const shippingDateMonth = new Date(offerShippingDate * 1000).getMonth();

				if (calendarDays[shippingDateDay] && calendarMonth[shippingDateMonth]) {
					if (new Date(offerShippingDate * 1000).toDateString() === new Date().toDateString() || new Date(offerShippingDate * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
						return new Date(offerShippingDate * 1000).getDate() + '.' + (new Date(offerShippingDate * 1000).getMonth() + 1) + '.';
					} else {
						return calendarDays[shippingDateDay] + ', ' + new Date(offerShippingDate * 1000).getDate() + '.' + (new Date(offerShippingDate * 1000).getMonth() + 1) + '.';
					}
				} else {
				return new Date(offerShippingDate * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
				}
			} else {
				return '';
			}
		});
	});

	//show locations
	const showLocations = computed(() => {
		if(flyoutMode.value == 'stores' && flyoutContent.value.warehouses_display && !flyoutContent.value.warehouses_single_pickup_display && (flyoutContent.value.status == 1 || flyoutContent.value.status == 4)) {
			return true
		} else {
			return false
		}
	});

	//select offer
	async function selectOffer(item) {
		emit('selectOffer', {'shopping_cart_code': item.shopping_cart_code});
		flyoutClose();
	}
	*/
</script>

<style lang="less" scoped>
	*{
		--flyoutSideOffset: 30px;
	}
	.flyout{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 999999; font-size: 15px;}
	.flyout-mask{position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5);}
	.flyout-body{background: #fff; width: 35%; position: fixed; top: 0; right: 0; bottom: 0; display: flex; flex-direction: column; z-index: 1;}
	.close{
		position: absolute; display: flex; align-items: center; justify-content: center; top: 30px; right: var(--flyoutSideOffset); width: 25px; height: 25px; font-size: 0; cursor: pointer;
		&:before{.icon-x(); font: 20px/1 var(--fonti);}
	}
</style>
