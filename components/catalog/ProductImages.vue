<template>
	<div class="cd-images" :class="{'age-verification': item.xxx && !isAdult}">
		<div v-if="item?.all_images.length > 1" class="cd-thumbs cd-thumbs-slider">
			<ClientOnly>
				<template v-for="(thumb, index) in item?.all_images.slice(0, maxImages)" :key="thumb.id">
					<div class="cd-thumb cd-thumb-image" v-if="thumb.file_thumbs?.['width110-height110']" @click="openGallery(index)">
						<BaseUiImage loading="lazy" :data="thumb?.file_thumbs?.['width110-height110']" default="/images/no-image.jpg" />
						<span v-if="index == maxImages - 1 && item?.all_images.length > maxImages" class="cd-thumb-more">
							<span>
								<span>+{{ item?.all_images.length - maxImages}}</span>
								<template v-if="item?.all_images.length - maxImages > 1 && item?.all_images.length - maxImages < 5"> slike</template>
								<template v-else> slika</template>
							</span>
						</span>
					</div>
				</template>
				<template v-if="videos?.length">
					<div class="cd-thumb cd-thumb-video" @click="openGallery(item.all_images.length)">
						<BaseUiImage loading="lazy" :data="item?.all_images[0]?.file_thumbs?.['width110-height110']" default="/images/no-image.jpg" />
						<span class="cd-thumb-more">
							<span>
								<span>{{ videos.length }}</span>
								<template v-if="videos.length == 1"> video</template>
								<template v-else> videa</template>
							</span>
						</span>
					</div>
				</template>
			</ClientOnly>
		</div>

		<div class="cd-hero-image" :class="{'single': item?.all_images?.length <= 1}">
			<template v-if="item?.all_images?.length">
				<BaseUiSwiper
					class="cd-hero-slider"
					name="hero"
					:options="{
						slidesPerView: 1,
						effect: 'fade',
						breakpoints: {
							980: {
								pagination: false
							}
						}
					}">
					<template v-for="(file, index) in item.all_images" :key="index">
						<BaseUiSwiperSlide v-if="file?.file_thumbs?.['width640-height640']" class="cd-hero-slide" rel="noreferrer" @click="openGallery(index)">
							<BaseUiImage loading="lazy" :data="file?.file_thumbs?.['width640-height640']" default="/images/no-image-640x640.webp" />
							<!-- image state badge -->
							<!-- FIXME INTEG stilizirati i provjeriti -->
							<span v-if="file.state == 'd'" class="btn-image-state" :class="{'special': item?.check_lists && item?.check_lists[webcatalog3D]}"><BaseCmsLabel code="condition_images_badge" tag="span" /></span>
						</BaseUiSwiperSlide>
					</template>
					<template v-if="videos?.length">
						<BaseUiSwiperSlide v-for="(video, index) in videos" :key="index" class="cd-hero-slide cd-hero-video" rel="noreferrer" @click="openGallery(item.all_images.length)">
							<video controls :src="absolute(video.url)" />
						</BaseUiSwiperSlide>
					</template>
				</BaseUiSwiper>

				<!-- wishlist -->
				<div class="cd-icons-top-right">
					<CmsShare />
					<CatalogSetWishlists :item="item" mode="detail" />
					<CatalogSetCompare :item="item" mode="detail" />
					<!-- 3d button -->
					<div v-if="item?.check_lists && item?.check_lists[webcatalog3D]" @click="bus.event = 'model-view'" class="cd-model-view"><BaseCmsLabel code="view_3d" tag="span" /></div>
				</div>

				<!-- energy -->
				<div v-if="energyAttr" class="cd-energy" :class="{'link': item?.energy_image_upload_path_thumb_energy}" @click="(energyImage) ? flyoutEnergy(item?.energy_image_upload_path_thumb_energy) : null">
					<BaseUiImage v-if="energyAttr.image_upload_path" :src="energyAttr.image_upload_path" width="60" height="32" default="/images/no-image-40.webp" :alt="energyAttr.title" />
					<template v-if="mobileBreakpoint && energyAttr.title && energyAttr?.energy_image_upload_path != null">{{ energyAttr.title }}</template>
				</div>

				<!-- 3d button -->
				<div v-if="!mobileBreakpoint && item?.check_lists && item?.check_lists[webcatalog3D]" @click="bus.event = 'model-view'" class="cd-model-view"><BaseCmsLabel code="view_3d" tag="span" /></div>
			</template>
			<template v-else>
				<img class="no-image" loading="lazy" src="assets/images/no-image-952.webp" :title="item.seo_h1" :alt="item.seo_h1" />
			</template>
			<CatalogAgeVerificationOverlay :item="props.item" class="cp-detail-image-age-verification" />
		</div>
	</div>

	<Teleport to="body">
		<LazyCatalogGalleryModal v-if="Object.keys(activeModals()).includes('gallery-modal')" />
	</Teleport>
</template>

<script setup>
	const config = useAppConfig();
	const {mobileBreakpoint} = inject('rwd');
	const {open: openModal, activeModals} = useModal();
	const {absolute} = useUrl();
	const props = defineProps(['item']);
	const origin = config.host == 'https://www.bigbang.si' || config.host == 'https://beta.bigbang.si' ? true : false;
	const webcatalog = origin ? '?check_lists=webcatalog_157271,webcatalog_157270,webcatalog_201949,webcatalog_1219026' : '?check_lists=webcatalog_157271,webcatalog_157270,webcatalog_201949,webcatalog_2225499';
	const webcatalog3D = origin ? 'webcatalog_1219026' : 'webcatalog_2225499';
	const {openAgeModal, isAdult} = useAgeVerification();

	function openGallery(index) {
		const images = (props.item?.all_images?.length) ? props.item.all_images : [];
		const videos = props.item?.documents?.filter(image => image.kind == 'video') || [];
		if(!images?.length && !videos?.length) return;
		openModal('gallery-modal', {
			startIndex: index,
			items: [...images, ...videos],
		});
	}

	const videos = computed(() => props.item?.documents?.filter(image => image.kind == 'video') || []);
	const maxImages = computed(() => videos.value.length ? 4 : 5);

	//energy
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35'];
		return props.item?.attributes_special?.find(attr => targetAttributeCodes?.includes(attr?.attribute_code)) || null;
	});
</script>


<style lang="less" scoped>
	//swiper
	:deep(.swiper-button){
		display: flex; align-items: center; justify-content: center; width: 46px; height: 46px; background: var(--white); border-radius: 100%; font-size: 0; line-height: 0; position: absolute; left: 110px; top: 50%; transform: translateY(-50%); z-index: 1; cursor: pointer; .transition(background); box-shadow: 0px 0.764px 3.055px 0px rgba(0, 0, 0, 0.25);
		&:before{.icon-arrow-down(); margin-right: 4px; font: 14px/1 var(--fonti); color: var(--gray5); position: absolute; z-index: 1; .rotate(90deg); .transition(color);}

		@media (max-width: @l){left: 16px;}
		@media (max-width: @m){display: none!important;}
	}
	:deep(.swiper-button-next){
		left: unset; right: 110px; transform: translateY(-50%);
		&:before{margin: 0 0 0 4px; .rotate(-90deg);}

		@media (max-width: @l){left: unset; right: 16px;}
	}
	:deep(.swiper-button-disabled){display: none;}

	:deep(.swiper-pagination){
		display: none;

		@media (max-width: @m){display: inline-flex; align-items: center; justify-content: center; position: absolute; bottom: 8px; left: 50%; transform: translateX(-50%); z-index: 1;}
	}
	:deep(.hero-swiper-dot){
		@media (max-width: @m){
			display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 10px; height: 10px; margin: 0 3px; background: var(--white); border: 1px solid var(--gray5); border-radius: 100%; z-index: 1; .transition(all);
			&.is-active{background: var(--blueDark); border-color: var(--blueDark);}
		}
	}

	//images
	.cd-images{
		display: flex; height: 546px;

		@media (max-width: @m){height: 280px;}
	}
	.cd-thumbs{
		width: 98px; max-height: 546px; flex-shrink: 0; margin-right: 24px;
		@media (max-width: @m){display: none!important;}
	}
	.cd-thumb{
		display: flex; align-items: center; position: relative; justify-content: center; flex-shrink: 0; width: 98px; height: 98px; margin-bottom: 14px; background: var(--white); border-radius: var(--borderRadius); overflow: hidden; cursor: pointer; overflow: hidden;
		:deep(img){display: block; width: auto; height: auto; max-width: 80%; max-height: 80%;}
	}
	.cd-thumb-more{display: flex; align-items: center; justify-content: center; background: rgba(0, 0, 0, 0.6); border-radius: 12px; font-size: 15px; font-weight: 600; color: var(--white); position: absolute; top: 0; bottom: 0; left: 0; right: 0; z-index: 1; text-align: center;}
	.cd-thumb-image .cd-thumb-more{
		span{display: block; line-height: 1.2;}
		span span{font-size: 24px; display: block;}
	}
	.cd-thumb-video .cd-thumb-more{
		&>span{
			position: relative;
			&:before{.icon-play(); font: 26px/1 var(--fonti); color: #fff; display: block; margin-bottom: 5px;}
		}
	}
	
	.cd-hero-image{
		width: 830px; background: var(--white); border-radius: var(--borderRadius); position: relative; overflow: hidden;
		&.single{width: 100%;}

		@media (max-width: @l){width: 578px;}
		@media (max-width: @t){width: 428px;}
		@media (max-width: @m){width: 100%; border-radius: 0;}
	}
	.cd-hero-slide{
		display: flex; align-items: center; justify-content: center; background: #fff; cursor: pointer; position: relative;
		a{display: flex; align-items: center; justify-content: center;}
		:deep(img){display: block; width: auto; height: auto; max-width: 90%; max-height: 90%;}
		&:after{.pseudo(45px,45px); .icon-zoomIn(); color: #6D6D6D; opacity: 0; font: 45px/1 var(--fonti); display: flex; align-items: center; justify-content: center; background: #fff; border-radius: 100px; box-shadow: 0px 0.65px 2.61px 0px #00000040;}
		&:hover:after{opacity: 1;}
		@media (max-width: @m){
			:deep(img){max-height: 280px; border-radius: 0;}
		}
	}
	.cd-hero-slider{
		height: 100%;
		:deep(.swiper){height: 100%;}
	}
	.no-image{
		display: block; width: auto; height: auto; max-width: 100%; max-height: 100%; border-radius: 12px;

		@media (max-width: @m){border-radius: 0;}
	}

	//images icons/badges
	.cd-icons-top-right{
		position: absolute; top: 16px; right: 16px; z-index: 1; display: flex; gap: 13px; flex-direction: column;

		@media (max-width: @m){top: 12px; right: 12px;}
	}
	.cd-energy{
		display: flex; align-items: center; justify-content: center; position: absolute; left: 16px; bottom: 16px; font-size: 14px; color: var(--black); z-index: 1;
		&.link{cursor: pointer;}
		:deep(img){display: block; width: auto; height: auto; max-width: 60px; max-height: 32px;}

		@media (max-width: @m){
			left: 12px; bottom: 12px; font-size: 12px;
			:deep(img){max-width: 40px; max-height: 22px;}
		}
	}
	.cd-model-view{
		display: flex; align-items: center; justify-content: center; height: 40px; padding: 5px 24px; background: var(--white); border-radius: 28px; font-size: 16px; font-weight: 500; color: var(--gray5); position: absolute; right: 16px; bottom: 16px; box-shadow: 0px 0.764px 3.055px 0px rgba(0, 0, 0, 0.25); z-index: 1; cursor: pointer;
		span{
			padding-left: 34px; position: relative;
			&:before{.icon-d(); font: 24px/1 var(--fonti); color: var(--gray5); position: absolute; left: 0;}
		}

		@media (max-width: @m){
			width: 36px; height: 36px; margin-top: 8px; padding: 0; border-radius: 100%; position: relative; top: unset; right: unset; left: unset; bottom: unset; box-shadow: 0px 0.5px 2px 0px rgba(0, 0, 0, 0.25);
			&:before{.icon-d(); font: 22px/1 var(--fonti); color: var(--gray5); position: absolute;}
			span{display: none;}
		}
	}
	video{width: 80%; height: 80%; display: block;}
	.age-verification :deep(img){filter: blur(5px);}
</style>