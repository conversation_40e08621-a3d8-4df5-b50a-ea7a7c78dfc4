<template>
	<!-- price -->
	<div v-if="item.price_custom > 0" class="cp-price">
		<template v-if="recommendedPrice">
			<div class="cp-current-price red"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom" /></div>
			<div v-if="item.loyalty_price_custom" class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
		</template>
		<template v-else-if="promoPrice">
			<div class="cp-current-price"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom" /></div>
		</template>
		<template v-else-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom) && ((!item.loyalty_price_custom || isLoyalty) || (isLoyalty && item.loyalty_price_custom && item.price_custom < item.loyalty_price_custom))">
			<span v-if="priceFrom" class="cp-old-price cp-price-label var"><BaseCmsLabel code='price_variation' /></span>
			<div class="cp-current-price cp-discount-price red"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom" /></div>
			<div class="cp-old-price line-through"><BaseUtilsFormatCurrency :price="item.basic_price_custom" /></div>
			<span class="cp-price-info">
				<span class="cp-price-tooltip"><BaseCmsLabel code='lowest_price' /></span>
			</span>
		</template>
		<template v-else-if="isLoyalty && item.loyalty_price_custom && item.loyalty_price_custom < item.basic_price_custom">
			<span v-if="priceFrom" class="cp-old-price cp-price-label var"><BaseCmsLabel code='price_variation' /></span>
			<div class="cp-current-price cp-discount-price blue"><BaseUtilsFormatCurrency :wrap=true :price="item.loyalty_price_custom" /></div>
			<div class="cp-old-price line-through">
				<template v-if="item.selected_price == 'recommended' && (item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom)">
					<BaseUtilsFormatCurrency :price="item.price_custom" />
				</template>
				<template v-else>
					<BaseUtilsFormatCurrency :price="item.basic_price_custom" />
				</template>
			</div>
		</template>
		<template v-else>
			<div class="cp-current-price"><BaseUtilsFormatCurrency :wrap=true :price="item.price_custom" /></div>
		</template>
		<div v-if="item.selected_price == 'uau'" class="cp-price-uau"></div>
	</div>
	
	<!-- FIXME badge dodatnih 20% uz kod -->
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item']);
	const {priceFrom, recommendedPrice, promoPrice, isLoyalty} = useProductData(props.item);
	const {formatCurrency} = useCurrency();
</script>

<style lang="less" scoped>
	.cp-price{
		display: flex; align-items: flex-end; padding-top: 16px; gap: 6px; line-height: 1;
		@media (max-width: @t){flex-wrap: wrap; row-gap: 2px;}
		.red{color: var(--errorColor);}
	}
	.cp-current-price{
		font-weight: 700; font-size: 22px;
		@media (max-width: @t){font-size: 16px; width: 100%;}
		.p-comma{display: none;}
		.p-d{font-size: 12px; vertical-align: text-top;}
	}
	.cp-old-price{
		font-size: 14px; color: var(--black); padding-bottom: 3px;
		@media (max-width: @t){font-size: 11px; padding: 0;}
	}
	.cp-price-info{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 11px; height: 11px; position: relative; margin-bottom: 8px;
		@media (max-width: @t){margin-bottom: 1px;}
		&:before{.icon-info(); font: 11px/1 var(--fonti); color: var(--black);}
		&:hover .cp-price-tooltip{display: flex;}
	}
	.cp-price-tooltip{
		display: none; align-items: center; white-space: nowrap; padding: 6px 8px; border-radius: 6px; background: var(--gray3); font-size: 11px; font-weight: normal; color: var(--black); position: absolute; right: -10px; bottom: calc(~"100% - -8px");
		&:before{.pseudo(8px,8px); background: var(--gray3); position: absolute; top: calc(~"100% - 5px"); right: 12px; z-index: 1; .rotate(45deg);}
	}
	.cp-price-uau{display: flex; align-items: center; justify-content: center; width: 53px; height: 25px; background: url(assets/images/uau-badge.svg) no-repeat center; background-size: auto 40px; z-index: 1;}
</style>