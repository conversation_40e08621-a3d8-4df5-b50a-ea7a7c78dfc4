<template>
	<slot 
		:item="props.item" 
		:displayedBadges="displayedBadges" 
		:installmentPrice="installmentPrice" 
		:priceFrom="priceFrom" 
		:priceSaved="priceSaved" 
		:energyAttr="energyAttr" 
		:promoPrice="promoPrice" 
		:isLoyalty="isLoyalty" 
		:recommendedPrice="recommendedPrice" 
	/>
</template>

<script setup>
	const props = defineProps(['item']);
	const auth = useAuth();
	
	// Dva slikovna badgea koji se prikazuju na iznad slike
	const displayedBadges = computed(() => {
		return [];
		return props.item?.badges_special_1 ? props.item?.badges_special_1?.slice(0, 2) : [];
	});

	//badge discount
	const priceSaved = computed(() => {
		if (props.item.selected_price == 'recommended' && (props.item.discount_percent_custom > 0 || props.item.price_custom < props.item.basic_price_custom)) {
			return props.item.price_custom - props.item.loyalty_price_custom;
		} else {
			return props.item.basic_price_custom - props.item.price_custom;
		}
	});

	//energy image
	const energyAttr = computed(() => {
		const targetAttributeCodes = ['ucinek-pranja-in-su-100218739', 'razred-energijske-u-100215480', 'razred-energijske-u-100176542', 'razred-energij-ucinkov-35'];
		return props.item?.attributes_special?.find(attr => targetAttributeCodes.includes(attr.attribute_code)) || null;
	});

	//conf items label
	const priceFrom = computed(() => {
		if (['advanced', 'configurable'].includes(props.item.type) && props.item.basic_price_custom > props.item.price_custom) {
			return true;
		} else {
			return false;
		}
	});

	//installments
	const installmentPrice = computed(() => {
		if (props.item?.installments_calculation && props.item?.installments_calculation?.regular) {
			const values = Object.values(props.item.installments_calculation.regular);
			return Math.min(...values);
		}
	});
		
	const isLoyalty = computed(() => {
		const user = auth.getUser();
		return user?.loyalty_code ? true : false;
	});

	const recommendedPrice = computed(() => {
		if (props.item.selected_price == 'recommended' && (props.item.discount_percent_custom > 0 || props.item.price_custom < props.item.basic_price_custom) && (!props.item.loyalty_price_custom || isLoyalty.value == false || (isLoyalty.value == true && props.item.loyalty_price_custom && props.item.price_custom < props.item.loyalty_price_custom))) return true;
		return false;
	});

	const promoPrice = computed(() => {
		if (props.item.selected_price == 'promotion2' && (!props.item.loyalty_price_custom || isLoyalty.value == false || (isLoyalty.value == true && props.item.loyalty_price_custom && props.item.price_custom < props.item.loyalty_price_custom))) return true;
		return false;
	});

	/*
	// FIXME xxx provjera - dodati klasu na item i stilizirati
	//XXX verification
	const {bus, emit} = useEventBus();
	let xxxVisibility = ref(props.item?.xxx);
	function verificationXXX(title, value) {
		emit('xxx', {'title': title, 'value': value});
	}

	function hasCookie(cookieName) {
		if (typeof document !== 'undefined' && props.item?.xxx) {
			const cookies = document.cookie.split(';');
			for (let i = 0; i < cookies.length; i++) {
				const cookie = cookies[i].trim();
				if (cookie.startsWith(cookieName + '=')) {
					xxxVisibility.value = false;
					return true;
				} else {
					xxxVisibility.value = true;
				}
			}
		}
		return false;
	}

	onMounted(() => {
		hasCookie('xxx_verification');
	});

	watch(
		() => bus.value,
		() => {
			if(bus.value?.event == 'xxx_verificated') {
				hasCookie('xxx_verification');
				xxxVisibility.value = false;
			}
		}
	);
	*/
</script>