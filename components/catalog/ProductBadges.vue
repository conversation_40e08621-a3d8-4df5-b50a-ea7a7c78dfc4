<template>
	<div class="cp-badges-wrapper" v-if="energyAttr || item.product_condition != 'n' || item.badges_special_1 || item.selected_price == 'uau' || (item.status == 5 && item.date_available)">
		<div class="cp-badges">
			<div v-if="energyAttr" class="cp-energy">
				<BaseUiImage v-if="energyAttr.image_upload_path" loading="lazy" :src="energyAttr.image_upload_path" default="/images/no-image.jpg" width="80" height="25" :title="energyAttr.title" :alt="energyAttr.title" />
				<span v-else>energyAttr.title</span>
			</div>
			<template v-if="displayedBadges?.length">
				<template v-for="badge in displayedBadges" :key="badge.code">
					<div v-if="badge.category == 0" class="cp-badge cp-badge-discount red">-<BaseUtilsFormatCurrency :price="priceSaved" /></div>
					<template v-if="badge.label_title">
						<div class="cp-badge" :class="['cp-badge-' + badge.code]">{{ badge.label_title }}</div>
					</template>
					<div v-if="badge.category == 4" class="cp-badge cp-badge-new"><BaseCmsLabel code='new_badge' /></div>
				</template>
			</template>
			<div v-if="item.selected_price == 'uau'" class="cp-badge cp-badge-uau blue"><BaseCmsLabel code='uau_badge_title' /></div>
			<div v-if="item.product_condition != 'n'" class="cp-badge cp-condition-badge yellow">
				<span v-html="labels.get('condition_' + item.product_condition)"></span>
			</div>
		</div>
	</div>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item']);
	const {displayedBadges, priceSaved, energyAttr} = useProductData(props.item);
</script>

<style scoped lang="less">
	.cp-badges-wrapper{position: absolute; bottom: 12px; left: 14px; right: 14px;}
	.cp-badges{display: flex; flex-wrap: wrap; gap: 6px; align-items: center;}
	.cp-badge{
		display: inline-flex; align-items: center; justify-content: center; min-height: 22px; padding: 2px 12px; background: var(--blueDark); border-radius: 4px; font-size: 12px; font-weight: 600; color: var(--white); white-space: nowrap; position: relative; z-index: 11;
		&.red{background: var(--errorColor);}
		&.blue{background: var(--blue);}
		&.yellow{background: #CDD700; color: var(--blueDark);}
	}
	.cp-energy{
		height: 20px; font-size: 12px; position: relative; z-index: 11; width: 100%;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
</style>