<template>
	<div class="posr">
		<div class="wrapper">
			<BaseUiSwiper :options="{modules: [Scrollbar], scrollbar: {el: '.swiper-scrollbar-'+props.name, hide: false, draggable: true}, ...props.options}">
				<slot />
			</BaseUiSwiper>
		</div>
	</div>
	<div class="wrapper swiper-scrollbar-wrapper">
		<div class="swiper-scrollbar" :class="'swiper-scrollbar-'+props.name"></div>
	</div>
</template>

<script setup>
	import {Scrollbar} from 'swiper/modules';
	const props = defineProps({
		options: Object,
		name: String
	})
</script>