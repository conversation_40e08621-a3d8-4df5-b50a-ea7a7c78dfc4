# Task

Install last viewed products feature by following this steps to the point:

# Steps

1. Add \*\*HAPI_TRACKING config to nuxt.config.ts:

    ```
    vite: {
    	define: {
    		__HAPI_TRACKING__: JSON.stringify(true),
    	},
    },
    ```

2. App catalogproduct tracking event app.config.ts:

    ```
    hapi: {
    	tracking: {
    		events: ['catalogproduct'],
    	},
    },
    ```

3. Add this widget to currently open template where %LAST_VIEWED% is:

    ```
    <BaseCatalogProductsWidget :fetch="{sort: 'last_view', special_view: 'viewed', id_exclude: item.id}" :cache="false" v-slot="{items}">
    ...
    </BaseCatalogProductsWidget>
    ```

    Do not add anything inside the widget. Leave it empty. Just add the widget as in the above example

Do not edit, delete or add any other code. Do not think. Just do. Focus only on the above steps.
