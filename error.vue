<template>
	<div>
		<div v-if="error.statusCode == 503 || error.message?.includes('/_nuxt/builds/meta/') || (error.statusCode == 500 && mode == 'production')">
			<Meta http-equiv="refresh" :content="error.message?.includes('/_nuxt/builds/meta/') ? '2' : '60'" />
			<Title v-if="config?.lang == 'si'">Nadgradnja</Title>
			<Title v-else>Nadogradnja</Title>
			<div style="text-align: center">
				<br /><img src="/images/logo-mail.png" width="200" /><br /><br />
				<template v-if="config?.lang == 'si'">Splet se posodablja. Na voljo bomo čez nekaj minut.</template>
				<template v-else>Web stranica je u procesu ažuriranja. Biti ćemo dostupni za nekoliko minuta.</template>
				<div style="padding-top: 30px">
					<button @click="reloadPage" style="margin: auto">
						<template v-if="config?.lang == 'si'">Osveži spletno stran</template>
						<template v-else>Osvježi stranicu</template>
					</button>
				</div>
			</div>
		</div>
		<div v-else-if="error.statusCode == 401 || (error.statusCode == 404 && error.message == 'unauthorized')">
			<BaseThemeUiLoading mode="fullpage" />
		</div>
		<div v-else class="error-page">
			<template v-if="mode == 'development'">
				<h1>Error {{ error.statusCode }}</h1>
				<h2 v-if="error.statusMessage">{{ error.statusMessage }}</h2>
				<h2 v-if="error.message">{{ error.message }}</h2>
				<div v-html="error.stack" />
				<br />
				<button @click="handleError">Clear error and redirect to homepage</button>
			</template>
		</div>
	</div>
</template>

<style scoped>
	.error-page {
		padding: 2%;
		font-size: 15px;
		line-height: 1.4;
	}
	img {
		height: auto;
		max-width: 200px;
	}
	h1 {
		font-size: 30px;
		padding: 0 0 20px;
		margin: 0;
		font-weight: bold;
	}
	h2 {
		font-size: 25px;
		margin: 0;
		padding: 0 0 10px;
		font-weight: normal;
	}
</style>

<script setup>
	const config = useAppConfig();
	const token = useToken();
	const props = defineProps({
		error: Object,
	});

	const mode = ref(process.env.NODE_ENV);

	if (props.error.statusCode == 503) {
		// check if maintenance mode is active. If not, reload page.
		const maintenance = await $fetch(`${config.host}/${config.api}/${config.apiVersion}/misc/is-maintenance/`, {
			headers: {
				'Authorization': 'Bearer ' + token.get(),
			},
		});

		if (!maintenance.success && process.client) {
			setTimeout(() => {
				window.location.reload();
			}, 5000);
		}
	}

	// unauthorized API calls. Handle tokens and reload page
	if (props.error.statusCode == 401) {
		await token.generateServerToken();
		token.removeToken();
		reloadNuxtApp({ttl: 2000});
	}

	// Handle unauthorized (hidden) pages
	let timeout;
	if (props.error.statusCode == 404 && props.error.message == 'unauthorized' && process.client) {
		if (timeout) clearTimeout(timeout);
		if (props.error.url) {
			timeout = setTimeout(() => {
				return navigateTo(props.error.url);
			}, 500);
		}
	}

	function reloadPage() {
		return reloadNuxtApp({ttl: 1000});
	}

	// in production, show 404 instead 500 error page
	if (![503, 401].includes(props.error.statusCode) && !props.error.message?.includes('/_nuxt/builds/meta/') && mode.value == 'production') {
		await navigateTo('/404/');
	}

	function handleError() {
		return clearError({redirect: '/'});
	}
</script>
